{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/MiningUnitsTable.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui';\nimport { MiningRig } from '@/components/icons';\nimport { Calendar, TrendingUp, DollarSign } from 'lucide-react';\nimport { formatCurrency, formatTHS, formatDateTime, formatNumber } from '@/lib/utils';\nimport { useClientOnly } from '@/hooks/useClientOnly';\n\ninterface MiningUnit {\n  id: string;\n  thsAmount: number;\n  investmentAmount: number;\n  startDate: string;\n  expiryDate: string;\n  dailyROI: number;\n  totalEarned: number;\n  miningEarnings: number;\n  referralEarnings: number;\n  binaryEarnings: number;\n  status: 'ACTIVE' | 'EXPIRED';\n  createdAt: string;\n}\n\nexport const MiningUnitsTable: React.FC = () => {\n  const [miningUnits, setMiningUnits] = useState<MiningUnit[]>([]);\n  const [loading, setLoading] = useState(true);\n  const isClient = useClientOnly();\n\n  useEffect(() => {\n    fetchMiningUnits();\n  }, []);\n\n  const fetchMiningUnits = async () => {\n    try {\n      const response = await fetch('/api/mining-units', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setMiningUnits(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch mining units:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const calculateProgress = (unit: MiningUnit) => {\n    const maxEarnings = unit.investmentAmount * 5;\n    const totalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n    const progress = (totalEarnings / maxEarnings) * 100;\n    return Math.min(progress, 100);\n  };\n\n  const getTotalEarnings = (unit: MiningUnit) => {\n    return unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;\n  };\n\n  const getRemainingCapacity = (unit: MiningUnit) => {\n    const maxEarnings = unit.investmentAmount * 5;\n    const totalEarnings = getTotalEarnings(unit);\n    return Math.max(0, maxEarnings - totalEarnings);\n  };\n\n  const getExpirationOrder = () => {\n    // Sort active units by creation date (FIFO order)\n    return miningUnits\n      .filter(unit => unit.status === 'ACTIVE')\n      .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())\n      .map((unit, index) => ({ unitId: unit.id, order: index + 1 }));\n  };\n\n  const getDaysRemaining = (expiryDate: string) => {\n    if (!isClient) return 0; // Return 0 on server to prevent hydration mismatch\n\n    const expiry = new Date(expiryDate);\n    const now = new Date();\n    const diffTime = expiry.getTime() - now.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(diffDays, 0);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'ACTIVE':\n        return 'bg-eco-100 text-eco-700';\n      case 'EXPIRED':\n        return 'bg-gray-100 text-gray-700';\n      default:\n        return 'bg-gray-100 text-gray-700';\n    }\n  };\n\n  if (loading) {\n    return (\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <MiningRig className=\"h-5 w-5 text-solar-500\" />\n            <span>Mining Units</span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"animate-pulse space-y-4\">\n            {Array.from({ length: 3 }).map((_, i) => (\n              <div key={i} className=\"h-16 bg-gray-200 rounded-lg\"></div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center space-x-2\">\n          <MiningRig className=\"h-5 w-5 text-solar-500\" />\n          <span>Mining Units ({miningUnits.length})</span>\n        </CardTitle>\n      </CardHeader>\n      <CardContent>\n        {miningUnits.length > 0 ? (\n          <div className=\"space-y-4\">\n            {/* Desktop Table */}\n            <div className=\"hidden md:block overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead>\n                  <tr className=\"border-b border-gray-200\">\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Order</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Mining Power</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Investment</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Earnings Breakdown</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Progress to 5x</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Remaining</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Status</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {miningUnits.map((unit) => {\n                    const expirationOrder = getExpirationOrder();\n                    const orderInfo = expirationOrder.find(o => o.unitId === unit.id);\n\n                    return (\n                      <tr key={unit.id} className=\"border-b border-gray-100 hover:bg-gray-50\">\n                        {/* FIFO Order */}\n                        <td className=\"py-4 px-4\">\n                          {unit.status === 'ACTIVE' && orderInfo ? (\n                            <div className=\"flex items-center space-x-2\">\n                              <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium\">\n                                #{orderInfo.order}\n                              </span>\n                              <span className=\"text-xs text-gray-500\">Next to expire</span>\n                            </div>\n                          ) : (\n                            <span className=\"text-gray-400 text-xs\">-</span>\n                          )}\n                        </td>\n\n                        {/* Mining Power */}\n                        <td className=\"py-4 px-4\">\n                          <div className=\"flex items-center space-x-2\">\n                            <MiningRig className=\"h-4 w-4 text-solar-500\" />\n                            <div>\n                              <span className=\"font-medium\">{formatTHS(unit.thsAmount)}</span>\n                              <div className=\"text-xs text-gray-500\">\n                                {formatNumber(unit.dailyROI, 2)}% daily\n                              </div>\n                            </div>\n                          </div>\n                        </td>\n\n                        {/* Investment */}\n                        <td className=\"py-4 px-4\">\n                          <span className=\"font-medium\">{formatCurrency(unit.investmentAmount)}</span>\n                        </td>\n\n                        {/* Earnings Breakdown */}\n                        <td className=\"py-4 px-4\">\n                          <div className=\"space-y-1\">\n                            <div className=\"flex justify-between text-xs\">\n                              <span className=\"text-gray-600\">Mining:</span>\n                              <span className=\"font-medium text-green-600\">{formatCurrency(unit.miningEarnings)}</span>\n                            </div>\n                            <div className=\"flex justify-between text-xs\">\n                              <span className=\"text-gray-600\">Referral:</span>\n                              <span className=\"font-medium text-blue-600\">{formatCurrency(unit.referralEarnings)}</span>\n                            </div>\n                            <div className=\"flex justify-between text-xs\">\n                              <span className=\"text-gray-600\">Binary:</span>\n                              <span className=\"font-medium text-purple-600\">{formatCurrency(unit.binaryEarnings)}</span>\n                            </div>\n                            <div className=\"border-t pt-1 flex justify-between text-sm font-medium\">\n                              <span>Total:</span>\n                              <span className=\"text-eco-600\">{formatCurrency(getTotalEarnings(unit))}</span>\n                            </div>\n                          </div>\n                        </td>\n\n                        {/* Progress to 5x */}\n                        <td className=\"py-4 px-4\">\n                          <div className=\"w-full bg-gray-200 rounded-full h-3\">\n                            <div\n                              className=\"bg-eco-500 h-3 rounded-full transition-all duration-300\"\n                              style={{ width: `${calculateProgress(unit)}%` }}\n                            ></div>\n                          </div>\n                          <div className=\"text-xs text-gray-500 mt-1 flex justify-between\">\n                            <span>{formatNumber(calculateProgress(unit), 1)}%</span>\n                            <span>{formatCurrency(unit.investmentAmount * 5)} max</span>\n                          </div>\n                        </td>\n\n                        {/* Remaining Capacity */}\n                        <td className=\"py-4 px-4\">\n                          <div className=\"text-center\">\n                            <span className=\"font-medium text-gray-700\">\n                              {formatCurrency(getRemainingCapacity(unit))}\n                            </span>\n                            <div className=\"text-xs text-gray-500\">\n                              remaining\n                            </div>\n                          </div>\n                        </td>\n\n                        {/* Status */}\n                        <td className=\"py-4 px-4\">\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(unit.status)}`}>\n                            {unit.status}\n                          </span>\n                        </td>\n                      </tr>\n                    );\n                  })}\n                </tbody>\n              </table>\n            </div>\n\n            {/* Mobile Cards */}\n            <div className=\"md:hidden space-y-4\">\n              {miningUnits.map((unit) => {\n                const expirationOrder = getExpirationOrder();\n                const orderInfo = expirationOrder.find(o => o.unitId === unit.id);\n\n                return (\n                  <div key={unit.id} className=\"bg-gray-50 rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <div className=\"flex items-center space-x-2\">\n                        <MiningRig className=\"h-5 w-5 text-solar-500\" />\n                        <span className=\"font-semibold\">{formatTHS(unit.thsAmount)}</span>\n                        {unit.status === 'ACTIVE' && orderInfo && (\n                          <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium\">\n                            #{orderInfo.order}\n                          </span>\n                        )}\n                      </div>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(unit.status)}`}>\n                        {unit.status}\n                      </span>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 gap-4 mb-3\">\n                      <div>\n                        <div className=\"text-xs text-gray-500\">Investment</div>\n                        <div className=\"font-medium\">{formatCurrency(unit.investmentAmount)}</div>\n                      </div>\n                      <div>\n                        <div className=\"text-xs text-gray-500\">Daily ROI</div>\n                        <div className=\"font-medium text-eco-600\">{formatNumber(unit.dailyROI, 2)}%</div>\n                      </div>\n                      <div>\n                        <div className=\"text-xs text-gray-500\">Remaining</div>\n                        <div className=\"font-medium text-orange-600\">{formatCurrency(getRemainingCapacity(unit))}</div>\n                      </div>\n                      <div>\n                        <div className=\"text-xs text-gray-500\">Max Earnings</div>\n                        <div className=\"font-medium\">{formatCurrency(unit.investmentAmount * 5)}</div>\n                      </div>\n                    </div>\n\n                    {/* Earnings Breakdown */}\n                    <div className=\"mb-3 p-3 bg-white rounded-lg\">\n                      <div className=\"text-xs font-medium text-gray-700 mb-2\">Earnings Breakdown</div>\n                      <div className=\"space-y-1\">\n                        <div className=\"flex justify-between text-xs\">\n                          <span className=\"text-gray-600\">Mining:</span>\n                          <span className=\"font-medium text-green-600\">{formatCurrency(unit.miningEarnings)}</span>\n                        </div>\n                        <div className=\"flex justify-between text-xs\">\n                          <span className=\"text-gray-600\">Referral:</span>\n                          <span className=\"font-medium text-blue-600\">{formatCurrency(unit.referralEarnings)}</span>\n                        </div>\n                        <div className=\"flex justify-between text-xs\">\n                          <span className=\"text-gray-600\">Binary:</span>\n                          <span className=\"font-medium text-purple-600\">{formatCurrency(unit.binaryEarnings)}</span>\n                        </div>\n                        <div className=\"border-t pt-1 flex justify-between text-sm font-medium\">\n                          <span>Total:</span>\n                          <span className=\"text-eco-600\">{formatCurrency(getTotalEarnings(unit))}</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"mb-2\">\n                      <div className=\"flex justify-between text-xs text-gray-500 mb-1\">\n                        <span>Progress to 5x</span>\n                        <span>{formatNumber(calculateProgress(unit), 1)}%</span>\n                      </div>\n                      <div className=\"w-full bg-gray-200 rounded-full h-3\">\n                        <div\n                          className=\"bg-eco-500 h-3 rounded-full transition-all duration-300\"\n                          style={{ width: `${calculateProgress(unit)}%` }}\n                        ></div>\n                      </div>\n                    </div>\n\n                    <div className=\"text-xs text-gray-500\">\n                      Started: {formatDateTime(unit.startDate)} • Expires: {formatDateTime(unit.expiryDate)}\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n\n            {/* Summary */}\n            <div className=\"mt-6 p-4 bg-solar-50 rounded-lg\">\n              <h4 className=\"font-medium text-dark-900 mb-3\">Mining Units Summary</h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm\">\n                <div>\n                  <span className=\"text-gray-600\">Total Mining Power: </span>\n                  <span className=\"font-medium\">\n                    {formatTHS(miningUnits.reduce((sum, unit) => sum + unit.thsAmount, 0))}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"text-gray-600\">Total Investment: </span>\n                  <span className=\"font-medium\">\n                    {formatCurrency(miningUnits.reduce((sum, unit) => sum + unit.investmentAmount, 0))}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"text-gray-600\">Active Units: </span>\n                  <span className=\"font-medium text-green-600\">\n                    {miningUnits.filter(unit => unit.status === 'ACTIVE').length}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"text-gray-600\">Expired Units: </span>\n                  <span className=\"font-medium text-red-600\">\n                    {miningUnits.filter(unit => unit.status === 'EXPIRED').length}\n                  </span>\n                </div>\n              </div>\n\n              {/* Earnings Breakdown Summary */}\n              <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                <h5 className=\"font-medium text-dark-900 mb-2\">Total Earnings Breakdown</h5>\n                <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-gray-600\">Mining Earnings: </span>\n                    <span className=\"font-medium text-green-600\">\n                      {formatCurrency(miningUnits.reduce((sum, unit) => sum + unit.miningEarnings, 0))}\n                    </span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-600\">Referral Earnings: </span>\n                    <span className=\"font-medium text-blue-600\">\n                      {formatCurrency(miningUnits.reduce((sum, unit) => sum + unit.referralEarnings, 0))}\n                    </span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-600\">Binary Earnings: </span>\n                    <span className=\"font-medium text-purple-600\">\n                      {formatCurrency(miningUnits.reduce((sum, unit) => sum + unit.binaryEarnings, 0))}\n                    </span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-600\">Total Earned: </span>\n                    <span className=\"font-medium text-eco-600\">\n                      {formatCurrency(miningUnits.reduce((sum, unit) => sum + getTotalEarnings(unit), 0))}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              {/* FIFO Information */}\n              {miningUnits.filter(unit => unit.status === 'ACTIVE').length > 1 && (\n                <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                  <h5 className=\"font-medium text-dark-900 mb-2\">FIFO Expiration Order</h5>\n                  <p className=\"text-xs text-gray-600 mb-2\">\n                    Mining units expire when they reach 5x their investment amount. Units expire in First-In-First-Out (FIFO) order.\n                  </p>\n                  <div className=\"text-sm\">\n                    <span className=\"text-gray-600\">Next to expire: </span>\n                    <span className=\"font-medium text-orange-600\">\n                      Unit #{getExpirationOrder()[0]?.order || 'N/A'}\n                      ({formatNumber(calculateProgress(miningUnits.find(u => u.id === getExpirationOrder()[0]?.unitId) || miningUnits[0]), 1)}% complete)\n                    </span>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <MiningRig className=\"h-16 w-16 text-gray-300 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Mining Units</h3>\n            <p className=\"text-gray-500 mb-4\">\n              You haven't purchased any mining units yet. Start mining to earn daily returns!\n            </p>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAEA;AACA;AAPA;;;;;;;AAwBO,MAAM,mBAA6B;IACxC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,eAAe,KAAK,IAAI;gBAC1B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,cAAc,KAAK,gBAAgB,GAAG;QAC5C,MAAM,gBAAgB,KAAK,cAAc,GAAG,KAAK,gBAAgB,GAAG,KAAK,cAAc;QACvF,MAAM,WAAW,AAAC,gBAAgB,cAAe;QACjD,OAAO,KAAK,GAAG,CAAC,UAAU;IAC5B;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO,KAAK,cAAc,GAAG,KAAK,gBAAgB,GAAG,KAAK,cAAc;IAC1E;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAc,KAAK,gBAAgB,GAAG;QAC5C,MAAM,gBAAgB,iBAAiB;QACvC,OAAO,KAAK,GAAG,CAAC,GAAG,cAAc;IACnC;IAEA,MAAM,qBAAqB;QACzB,kDAAkD;QAClD,OAAO,YACJ,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAC/B,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,IAC9E,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAAE,QAAQ,KAAK,EAAE;gBAAE,OAAO,QAAQ;YAAE,CAAC;IAChE;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,UAAU,OAAO,GAAG,mDAAmD;QAE5E,MAAM,SAAS,IAAI,KAAK;QACxB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,OAAO,OAAO,KAAK,IAAI,OAAO;QAC/C,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAC1D,OAAO,KAAK,GAAG,CAAC,UAAU;IAC5B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC,gIAAA,CAAA,OAAI;;8BACH,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,wIAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;8BAGV,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;gCAAY,WAAU;+BAAb;;;;;;;;;;;;;;;;;;;;;IAMtB;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC,wIAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;;gCAAK;gCAAe,YAAY,MAAM;gCAAC;;;;;;;;;;;;;;;;;;0BAG5C,8OAAC,gIAAA,CAAA,cAAW;0BACT,YAAY,MAAM,GAAG,kBACpB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;kDACC,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,8OAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,8OAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,8OAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,8OAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,8OAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,8OAAC;oDAAG,WAAU;8DAAgD;;;;;;;;;;;;;;;;;kDAGlE,8OAAC;kDACE,YAAY,GAAG,CAAC,CAAC;4CAChB,MAAM,kBAAkB;4CACxB,MAAM,YAAY,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,KAAK,EAAE;4CAEhE,qBACE,8OAAC;gDAAiB,WAAU;;kEAE1B,8OAAC;wDAAG,WAAU;kEACX,KAAK,MAAM,KAAK,YAAY,0BAC3B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;;wEAAuE;wEACnF,UAAU,KAAK;;;;;;;8EAEnB,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;;;;;;iFAG1C,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;kEAK5C,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,wIAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,8OAAC;;sFACC,8OAAC;4EAAK,WAAU;sFAAe,CAAA,GAAA,mHAAA,CAAA,YAAS,AAAD,EAAE,KAAK,SAAS;;;;;;sFACvD,8OAAC;4EAAI,WAAU;;gFACZ,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,QAAQ,EAAE;gFAAG;;;;;;;;;;;;;;;;;;;;;;;;kEAOxC,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAK,WAAU;sEAAe,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,gBAAgB;;;;;;;;;;;kEAIrE,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC;4EAAK,WAAU;sFAA8B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,cAAc;;;;;;;;;;;;8EAElF,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC;4EAAK,WAAU;sFAA6B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,gBAAgB;;;;;;;;;;;;8EAEnF,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC;4EAAK,WAAU;sFAA+B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,cAAc;;;;;;;;;;;;8EAEnF,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAK;;;;;;sFACN,8OAAC;4EAAK,WAAU;sFAAgB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;kEAMtE,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,OAAO,GAAG,kBAAkB,MAAM,CAAC,CAAC;oEAAC;;;;;;;;;;;0EAGlD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;4EAAM,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,kBAAkB,OAAO;4EAAG;;;;;;;kFAChD,8OAAC;;4EAAM,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,gBAAgB,GAAG;4EAAG;;;;;;;;;;;;;;;;;;;kEAKrD,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,qBAAqB;;;;;;8EAEvC,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;kEAO3C,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,KAAK,MAAM,GAAG;sEACzF,KAAK,MAAM;;;;;;;;;;;;+CApFT,KAAK,EAAE;;;;;wCAyFpB;;;;;;;;;;;;;;;;;sCAMN,8OAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC;gCAChB,MAAM,kBAAkB;gCACxB,MAAM,YAAY,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,KAAK,EAAE;gCAEhE,qBACE,8OAAC;oCAAkB,WAAU;;sDAC3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,wIAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,8OAAC;4DAAK,WAAU;sEAAiB,CAAA,GAAA,mHAAA,CAAA,YAAS,AAAD,EAAE,KAAK,SAAS;;;;;;wDACxD,KAAK,MAAM,KAAK,YAAY,2BAC3B,8OAAC;4DAAK,WAAU;;gEAAuE;gEACnF,UAAU,KAAK;;;;;;;;;;;;;8DAIvB,8OAAC;oDAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,KAAK,MAAM,GAAG;8DACzF,KAAK,MAAM;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,8OAAC;4DAAI,WAAU;sEAAe,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,gBAAgB;;;;;;;;;;;;8DAEpE,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,8OAAC;4DAAI,WAAU;;gEAA4B,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,QAAQ,EAAE;gEAAG;;;;;;;;;;;;;8DAE5E,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,8OAAC;4DAAI,WAAU;sEAA+B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,qBAAqB;;;;;;;;;;;;8DAEpF,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,8OAAC;4DAAI,WAAU;sEAAe,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,gBAAgB,GAAG;;;;;;;;;;;;;;;;;;sDAKzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAyC;;;;;;8DACxD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAA8B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,cAAc;;;;;;;;;;;;sEAElF,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAA6B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,gBAAgB;;;;;;;;;;;;sEAEnF,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAA+B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,cAAc;;;;;;;;;;;;sEAEnF,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;oEAAK,WAAU;8EAAgB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;sDAKtE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;;gEAAM,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,kBAAkB,OAAO;gEAAG;;;;;;;;;;;;;8DAElD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,GAAG,kBAAkB,MAAM,CAAC,CAAC;wDAAC;;;;;;;;;;;;;;;;;sDAKpD,8OAAC;4CAAI,WAAU;;gDAAwB;gDAC3B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,SAAS;gDAAE;gDAAa,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,UAAU;;;;;;;;mCAxE9E,KAAK,EAAE;;;;;4BA4ErB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,mHAAA,CAAA,YAAS,AAAD,EAAE,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,SAAS,EAAE;;;;;;;;;;;;sDAGvE,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,gBAAgB,EAAE;;;;;;;;;;;;sDAGnF,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DACb,YAAY,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAAU,MAAM;;;;;;;;;;;;sDAGhE,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DACb,YAAY,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;;;;;;;8CAMnE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,cAAc,EAAE;;;;;;;;;;;;8DAGjF,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,gBAAgB,EAAE;;;;;;;;;;;;8DAGnF,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,cAAc,EAAE;;;;;;;;;;;;8DAGjF,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,iBAAiB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;gCAOvF,YAAY,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAAU,MAAM,GAAG,mBAC7D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;;wDAA8B;wDACrC,oBAAoB,CAAC,EAAE,EAAE,SAAS;wDAAM;wDAC7C,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,kBAAkB,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,oBAAoB,CAAC,EAAE,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG;wDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yCAQpI,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wIAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;;AAQ9C", "debugId": null}}]}