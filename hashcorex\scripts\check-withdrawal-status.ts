import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkWithdrawalStatus() {
  console.log('🔍 Checking withdrawal status synchronization...\n');

  try {
    // Get all withdrawal requests
    const withdrawalRequests = await prisma.withdrawalRequest.findMany({
      include: {
        user: {
          select: { email: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`Found ${withdrawalRequests.length} withdrawal requests:\n`);

    for (const withdrawal of withdrawalRequests) {
      console.log(`📋 Withdrawal Request: ${withdrawal.id}`);
      console.log(`   User: ${withdrawal.user.email}`);
      console.log(`   Amount: $${withdrawal.amount}`);
      console.log(`   Status: ${withdrawal.status}`);
      console.log(`   Created: ${withdrawal.createdAt}`);
      if (withdrawal.rejectionReason) {
        console.log(`   Rejection Reason: ${withdrawal.rejectionReason}`);
      }
      if (withdrawal.processedAt) {
        console.log(`   Processed: ${withdrawal.processedAt}`);
      }

      // Find corresponding transaction
      const relatedTransaction = await prisma.transaction.findFirst({
        where: {
          reference: withdrawal.id,
          type: 'WITHDRAWAL'
        }
      });

      if (relatedTransaction) {
        console.log(`   📊 Related Transaction: ${relatedTransaction.id}`);
        console.log(`      Transaction Status: ${relatedTransaction.status}`);
        console.log(`      Transaction Amount: $${relatedTransaction.amount}`);
        console.log(`      Transaction Description: ${relatedTransaction.description}`);
        console.log(`      Transaction Created: ${relatedTransaction.createdAt}`);

        // Check for status mismatch
        const expectedTransactionStatus = withdrawal.status === 'REJECTED' ? 'FAILED' :
                                        withdrawal.status === 'APPROVED' ? 'COMPLETED' :
                                        withdrawal.status === 'COMPLETED' ? 'COMPLETED' : 'PENDING';

        if (relatedTransaction.status !== expectedTransactionStatus) {
          console.log(`   ❌ STATUS MISMATCH!`);
          console.log(`      Withdrawal Status: ${withdrawal.status}`);
          console.log(`      Transaction Status: ${relatedTransaction.status}`);
          console.log(`      Expected Transaction Status: ${expectedTransactionStatus}`);
        } else {
          console.log(`   ✅ Status synchronized correctly`);
        }
      } else {
        console.log(`   ❌ No related transaction found!`);
      }

      console.log('');
    }

    // Check for orphaned withdrawal transactions (transactions without withdrawal requests)
    console.log('\n🔍 Checking for orphaned withdrawal transactions...');
    
    const withdrawalTransactions = await prisma.transaction.findMany({
      where: { type: 'WITHDRAWAL' },
      include: {
        user: {
          select: { email: true }
        }
      }
    });

    for (const transaction of withdrawalTransactions) {
      if (transaction.reference) {
        const withdrawalRequest = await prisma.withdrawalRequest.findUnique({
          where: { id: transaction.reference }
        });

        if (!withdrawalRequest) {
          console.log(`❌ Orphaned transaction: ${transaction.id}`);
          console.log(`   User: ${transaction.user.email}`);
          console.log(`   Reference: ${transaction.reference}`);
          console.log(`   Status: ${transaction.status}`);
        }
      } else {
        console.log(`❌ Transaction without reference: ${transaction.id}`);
        console.log(`   User: ${transaction.user.email}`);
        console.log(`   Status: ${transaction.status}`);
      }
    }

    console.log('\n🎉 Withdrawal status check completed!');

  } catch (error) {
    console.error('❌ Error during status check:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkWithdrawalStatus()
  .then(() => {
    console.log('✅ Check completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Check failed:', error);
    process.exit(1);
  });
