import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testTransactionLogic() {
  console.log('🧪 Testing transaction logic...\n');

  try {
    // Test 1: Check for duplicate deposits
    console.log('1. Checking for duplicate deposit transactions...');
    
    const duplicateDeposits = await prisma.transaction.groupBy({
      by: ['userId', 'description'],
      where: {
        type: 'DEPOSIT',
        description: {
          contains: 'USDT TRC20 Deposit - TX:'
        }
      },
      _count: {
        id: true
      },
      having: {
        id: {
          _count: {
            gt: 1
          }
        }
      }
    });

    if (duplicateDeposits.length === 0) {
      console.log('✅ No duplicate deposit transactions found');
    } else {
      console.log(`❌ Found ${duplicateDeposits.length} groups with duplicate deposits`);
      duplicateDeposits.forEach(group => {
        console.log(`   - User ${group.userId}: ${group._count.id} duplicates`);
      });
    }

    // Test 2: Check for duplicate withdrawals
    console.log('\n2. Checking for duplicate withdrawal transactions...');
    
    const duplicateWithdrawals = await prisma.transaction.groupBy({
      by: ['userId', 'reference'],
      where: {
        type: 'WITHDRAWAL',
        reference: { not: null }
      },
      _count: {
        id: true
      },
      having: {
        id: {
          _count: {
            gt: 1
          }
        }
      }
    });

    if (duplicateWithdrawals.length === 0) {
      console.log('✅ No duplicate withdrawal transactions found');
    } else {
      console.log(`❌ Found ${duplicateWithdrawals.length} groups with duplicate withdrawals`);
      duplicateWithdrawals.forEach(group => {
        console.log(`   - User ${group.userId}, Reference ${group.reference}: ${group._count.id} duplicates`);
      });
    }

    // Test 3: Check withdrawal transaction status consistency
    console.log('\n3. Checking withdrawal transaction status consistency...');
    
    const withdrawalRequests = await prisma.withdrawalRequest.findMany({
      where: {
        status: { in: ['APPROVED', 'COMPLETED', 'REJECTED'] }
      },
      include: {
        user: {
          select: { email: true }
        }
      }
    });

    let inconsistentCount = 0;

    for (const request of withdrawalRequests) {
      const transaction = await prisma.transaction.findFirst({
        where: {
          reference: request.id,
          type: 'WITHDRAWAL',
        }
      });

      if (transaction) {
        let expectedStatus: 'COMPLETED' | 'CANCELLED' = 'COMPLETED';
        
        if (request.status === 'REJECTED') {
          expectedStatus = 'CANCELLED';
        }

        if (transaction.status !== expectedStatus) {
          console.log(`   ❌ Inconsistent status for ${request.user.email}:`);
          console.log(`      Request: ${request.status}, Transaction: ${transaction.status}`);
          inconsistentCount++;
        }
      } else {
        console.log(`   ⚠️  No transaction found for withdrawal request ${request.id} (${request.user.email})`);
        inconsistentCount++;
      }
    }

    if (inconsistentCount === 0) {
      console.log('✅ All withdrawal transaction statuses are consistent');
    } else {
      console.log(`❌ Found ${inconsistentCount} inconsistent withdrawal statuses`);
    }

    // Test 4: Check for orphaned transactions
    console.log('\n4. Checking for orphaned transactions...');
    
    const orphanedWithdrawals = await prisma.transaction.findMany({
      where: {
        type: 'WITHDRAWAL',
        reference: { not: null }
      }
    });

    let orphanedCount = 0;

    for (const transaction of orphanedWithdrawals) {
      if (transaction.reference) {
        const request = await prisma.withdrawalRequest.findUnique({
          where: { id: transaction.reference }
        });

        if (!request) {
          console.log(`   ❌ Orphaned withdrawal transaction: ${transaction.id} (reference: ${transaction.reference})`);
          orphanedCount++;
        }
      }
    }

    if (orphanedCount === 0) {
      console.log('✅ No orphaned withdrawal transactions found');
    } else {
      console.log(`❌ Found ${orphanedCount} orphaned withdrawal transactions`);
    }

    // Test 5: Summary statistics
    console.log('\n5. Transaction summary statistics...');
    
    const stats = await prisma.transaction.groupBy({
      by: ['type', 'status'],
      _count: {
        id: true
      },
      _sum: {
        amount: true
      }
    });

    console.log('   Transaction counts by type and status:');
    stats.forEach(stat => {
      console.log(`   - ${stat.type} (${stat.status}): ${stat._count.id} transactions, $${stat._sum.amount?.toFixed(2) || '0.00'} total`);
    });

    console.log('\n🎉 Transaction logic test completed!');

  } catch (error) {
    console.error('❌ Error during transaction logic test:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testTransactionLogic()
  .then(() => {
    console.log('✅ Test completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
