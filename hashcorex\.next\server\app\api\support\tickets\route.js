const CHUNK_PUBLIC_PATH = "server/app/api/support/tickets/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_lib_referral_ts_9176eeab._.js");
runtime.loadChunk("server/chunks/node_modules_41dbb181._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__fde4db12._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/support/tickets/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/support/tickets/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/support/tickets/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
