import { depositVerificationService } from '@/lib/depositVerificationService';

async function restartDepositService() {
  console.log('🔄 Restarting deposit verification service...\n');

  try {
    // Stop the service if it's running
    console.log('⏹️  Stopping current service...');
    await depositVerificationService.stop();
    
    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Start the service
    console.log('▶️  Starting deposit verification service...');
    await depositVerificationService.start();
    
    // Get service status
    const status = depositVerificationService.getStatus();
    console.log('📊 Service Status:');
    console.log(`   Running: ${status.isRunning}`);
    console.log(`   Active Verifications: ${status.activeVerifications}`);
    console.log(`   Confirmation Checks: ${status.confirmationChecks}`);
    
    console.log('\n✅ Deposit verification service restarted successfully!');
    console.log('   The service will now process any pending deposits.');
    console.log('   Transaction verification should work correctly now.');

  } catch (error) {
    console.error('❌ Error restarting deposit service:', error);
  }
}

// Run the restart
restartDepositService().catch(console.error);
