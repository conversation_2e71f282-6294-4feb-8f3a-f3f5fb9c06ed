import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function analyzeDuplicates() {
  console.log('🔍 Analyzing duplicate transactions in detail...\n');

  try {
    // 1. Get all transactions with detailed info
    console.log('1. Fetching all transactions...');
    
    const allTransactions = await prisma.transaction.findMany({
      where: {
        type: { in: ['DEPOSIT', 'WITHDRAWAL'] }
      },
      orderBy: [
        { userId: 'asc' },
        { type: 'asc' },
        { createdAt: 'asc' }
      ],
      include: {
        user: {
          select: { email: true }
        }
      }
    });

    console.log(`Found ${allTransactions.length} deposit/withdrawal transactions\n`);

    // 2. Analyze deposits in detail
    console.log('2. Analyzing deposit transactions...');
    
    const deposits = allTransactions.filter(t => t.type === 'DEPOSIT');
    console.log(`Total deposit transactions: ${deposits.length}`);

    // Group deposits by description (transaction ID)
    const depositGroups = new Map<string, typeof deposits>();
    deposits.forEach(tx => {
      const key = `${tx.userId}-${tx.description}`;
      if (!depositGroups.has(key)) {
        depositGroups.set(key, []);
      }
      depositGroups.get(key)!.push(tx);
    });

    console.log('\nDeposit transaction details:');
    for (const [key, transactions] of depositGroups) {
      if (transactions.length > 1) {
        console.log(`\n❌ DUPLICATE FOUND: ${key}`);
        console.log(`   User: ${transactions[0].user?.email}`);
        console.log(`   Count: ${transactions.length} transactions`);
        
        transactions.forEach((tx, index) => {
          console.log(`   ${index + 1}. ID: ${tx.id}`);
          console.log(`      Amount: $${tx.amount}`);
          console.log(`      Status: ${tx.status}`);
          console.log(`      Created: ${tx.createdAt}`);
          console.log(`      Description: ${tx.description}`);
          console.log(`      Reference: ${tx.reference || 'null'}`);
        });
      } else {
        console.log(`✅ ${key}: 1 transaction (${transactions[0].status})`);
      }
    }

    // 3. Analyze withdrawals in detail
    console.log('\n\n3. Analyzing withdrawal transactions...');
    
    const withdrawals = allTransactions.filter(t => t.type === 'WITHDRAWAL');
    console.log(`Total withdrawal transactions: ${withdrawals.length}`);

    // Group withdrawals by reference (withdrawal request ID)
    const withdrawalGroups = new Map<string, typeof withdrawals>();
    withdrawals.forEach(tx => {
      const key = `${tx.userId}-${tx.reference || 'no-ref'}`;
      if (!withdrawalGroups.has(key)) {
        withdrawalGroups.set(key, []);
      }
      withdrawalGroups.get(key)!.push(tx);
    });

    console.log('\nWithdrawal transaction details:');
    for (const [key, transactions] of withdrawalGroups) {
      if (transactions.length > 1) {
        console.log(`\n❌ DUPLICATE FOUND: ${key}`);
        console.log(`   User: ${transactions[0].user?.email}`);
        console.log(`   Count: ${transactions.length} transactions`);
        
        transactions.forEach((tx, index) => {
          console.log(`   ${index + 1}. ID: ${tx.id}`);
          console.log(`      Amount: $${tx.amount}`);
          console.log(`      Status: ${tx.status}`);
          console.log(`      Created: ${tx.createdAt}`);
          console.log(`      Description: ${tx.description}`);
          console.log(`      Reference: ${tx.reference || 'null'}`);
        });
      } else {
        console.log(`✅ ${key}: 1 transaction (${transactions[0].status})`);
      }
    }

    // 4. Check withdrawal requests vs transactions
    console.log('\n\n4. Checking withdrawal request consistency...');
    
    const withdrawalRequests = await prisma.withdrawalRequest.findMany({
      include: {
        user: {
          select: { email: true }
        }
      }
    });

    console.log(`Found ${withdrawalRequests.length} withdrawal requests`);

    for (const request of withdrawalRequests) {
      const relatedTransactions = await prisma.transaction.findMany({
        where: {
          reference: request.id,
          type: 'WITHDRAWAL'
        }
      });

      console.log(`\nWithdrawal Request ${request.id} (${request.user.email}):`);
      console.log(`   Status: ${request.status}`);
      console.log(`   Amount: $${request.amount}`);
      console.log(`   Created: ${request.createdAt}`);
      console.log(`   Related transactions: ${relatedTransactions.length}`);
      
      relatedTransactions.forEach((tx, index) => {
        console.log(`   Transaction ${index + 1}:`);
        console.log(`      ID: ${tx.id}`);
        console.log(`      Amount: $${tx.amount}`);
        console.log(`      Status: ${tx.status}`);
        console.log(`      Created: ${tx.createdAt}`);
      });
    }

    // 5. Check deposit transactions vs deposit records
    console.log('\n\n5. Checking deposit transaction consistency...');
    
    const depositRecords = await prisma.depositTransaction.findMany({
      include: {
        user: {
          select: { email: true }
        }
      }
    });

    console.log(`Found ${depositRecords.length} deposit records`);

    for (const deposit of depositRecords) {
      const relatedTransactions = await prisma.transaction.findMany({
        where: {
          userId: deposit.userId,
          type: 'DEPOSIT',
          description: {
            contains: deposit.transactionId
          }
        }
      });

      console.log(`\nDeposit Record ${deposit.transactionId} (${deposit.user.email}):`);
      console.log(`   Status: ${deposit.status}`);
      console.log(`   Amount: $${deposit.amount}`);
      console.log(`   Created: ${deposit.createdAt}`);
      console.log(`   Related transactions: ${relatedTransactions.length}`);
      
      relatedTransactions.forEach((tx, index) => {
        console.log(`   Transaction ${index + 1}:`);
        console.log(`      ID: ${tx.id}`);
        console.log(`      Amount: $${tx.amount}`);
        console.log(`      Status: ${tx.status}`);
        console.log(`      Created: ${tx.createdAt}`);
      });
    }

    console.log('\n🎉 Analysis completed!');

  } catch (error) {
    console.error('❌ Error during analysis:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the analysis
analyzeDuplicates()
  .then(() => {
    console.log('✅ Analysis completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Analysis failed:', error);
    process.exit(1);
  });
