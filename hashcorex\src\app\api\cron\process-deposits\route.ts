import { NextRequest, NextResponse } from 'next/server';
import {
  depositTransactionDb,
  walletBalanceDb,
  transactionDb,
  adminSettingsDb,
  systemLogDb
} from '@/lib/database';
import { prisma } from '@/lib/prisma';
import { verifyUSDTTransaction } from '@/lib/trongrid';

// POST - Process pending deposits and update confirmations
export async function POST(request: NextRequest) {
  try {
    // Verify cron secret
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    
    if (!cronSecret || authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('Starting deposit processing cron job...');

    // Get minimum confirmations setting
    const minConfirmations = parseInt(await adminSettingsDb.get('minConfirmations') || '10');
    
    // Get all pending deposits
    const pendingDeposits = await depositTransactionDb.findByStatus('PENDING');
    console.log(`Found ${pendingDeposits.length} pending deposits to process`);

    let processedCount = 0;
    let completedCount = 0;
    let failedCount = 0;

    for (const deposit of pendingDeposits) {
      try {
        console.log(`Processing deposit ${deposit.transactionId} for user ${deposit.userId}`);

        // Re-verify the transaction to get updated confirmation count
        const verificationResult = await verifyUSDTTransaction(
          deposit.transactionId,
          deposit.tronAddress,
          1 // Use 1 to get current confirmation count without validation
        );

        // Update the deposit record with current confirmation count
        await depositTransactionDb.updateConfirmations(
          deposit.transactionId,
          verificationResult.confirmations
        );

        console.log(`Transaction ${deposit.transactionId} has ${verificationResult.confirmations} confirmations (required: ${minConfirmations})`);

        // Check if transaction now has enough confirmations
        if (verificationResult.confirmations >= minConfirmations && verificationResult.isValid) {
          console.log(`Transaction ${deposit.transactionId} has enough confirmations, processing...`);

          // Update deposit status to confirmed
          await depositTransactionDb.updateStatus(deposit.transactionId, 'CONFIRMED', {
            verifiedAt: new Date(),
            processedAt: new Date(),
          });

          // Credit user's wallet balance
          await walletBalanceDb.addDeposit(deposit.userId, deposit.amount);

          // Check if there's already a pending transaction for this deposit
          const existingTransaction = await prisma.transaction.findFirst({
            where: {
              userId: deposit.userId,
              type: 'DEPOSIT',
              description: `USDT TRC20 Deposit - TX: ${deposit.transactionId}`,
              status: 'PENDING',
            },
          });

          if (existingTransaction) {
            // Update existing pending transaction to completed
            await prisma.transaction.update({
              where: { id: existingTransaction.id },
              data: {
                status: 'COMPLETED',
                amount: deposit.amount, // Update amount in case it was different
              },
            });
          } else {
            // Create new transaction record for balance tracking (fallback)
            await transactionDb.create({
              userId: deposit.userId,
              type: 'DEPOSIT',
              amount: deposit.amount,
              description: `USDT TRC20 Deposit - TX: ${deposit.transactionId}`,
              status: 'COMPLETED',
            });
          }

          // Log the completion
          await systemLogDb.create({
            action: 'DEPOSIT_AUTO_COMPLETED',
            userId: deposit.userId,
            details: {
              transactionId: deposit.transactionId,
              amount: deposit.amount,
              confirmations: verificationResult.confirmations,
              minConfirmations,
              processedBy: 'CRON_JOB',
            },
          });

          completedCount++;
          console.log(`✅ Completed deposit ${deposit.transactionId} for ${deposit.amount} USDT`);
        } else if (!verificationResult.isValid) {
          // Transaction is invalid, mark as failed
          await depositTransactionDb.markAsFailed(
            deposit.transactionId,
            `Transaction verification failed: ${verificationResult.confirmations} confirmations, invalid transaction`
          );

          await systemLogDb.create({
            action: 'DEPOSIT_AUTO_FAILED',
            userId: deposit.userId,
            details: {
              transactionId: deposit.transactionId,
              amount: deposit.amount,
              confirmations: verificationResult.confirmations,
              reason: 'Transaction verification failed',
              processedBy: 'CRON_JOB',
            },
          });

          failedCount++;
          console.log(`❌ Failed deposit ${deposit.transactionId} - invalid transaction`);
        } else {
          console.log(`⏳ Deposit ${deposit.transactionId} still pending - ${verificationResult.confirmations}/${minConfirmations} confirmations`);
        }

        processedCount++;

      } catch (error) {
        console.error(`Error processing deposit ${deposit.transactionId}:`, error);
        failedCount++;
      }
    }

    // Log the cron job execution
    await systemLogDb.create({
      action: 'DEPOSIT_PROCESSING_CRON_EXECUTED',
      details: {
        totalPending: pendingDeposits.length,
        processed: processedCount,
        completed: completedCount,
        failed: failedCount,
        minConfirmations,
        executionTime: new Date().toISOString(),
      },
    });

    console.log(`Deposit processing completed: ${processedCount} processed, ${completedCount} completed, ${failedCount} failed`);

    return NextResponse.json({
      success: true,
      message: 'Deposit processing completed',
      data: {
        totalPending: pendingDeposits.length,
        processed: processedCount,
        completed: completedCount,
        failed: failedCount,
        minConfirmations,
      },
    });

  } catch (error) {
    console.error('Deposit processing cron error:', error);
    
    // Log the error
    await systemLogDb.create({
      action: 'DEPOSIT_PROCESSING_CRON_ERROR',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
    });

    return NextResponse.json(
      { success: false, error: 'Deposit processing failed' },
      { status: 500 }
    );
  }
}
