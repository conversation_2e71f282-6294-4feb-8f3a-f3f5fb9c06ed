import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testTransactionAPI() {
  console.log('🧪 Testing transaction API logic...\n');

  try {
    // Get a test user
    const testUser = await prisma.user.findFirst({
      where: {
        email: { contains: '@' }
      }
    });

    if (!testUser) {
      console.log('❌ No test user found');
      return;
    }

    console.log(`Testing with user: ${testUser.email} (${testUser.id})`);

    // Simulate the API logic
    const transactions = await prisma.transaction.findMany({
      where: { userId: testUser.id },
      orderBy: { createdAt: 'desc' },
      take: 50,
    });

    console.log(`\nFound ${transactions.length} transactions in Transaction table:`);
    
    const formattedTransactions = transactions
      .filter(tx => tx.type !== 'ADMIN_CREDIT' && tx.type !== 'ADMIN_DEBIT')
      .map(tx => {
        if (tx.type === 'DEPOSIT') {
          const txidMatch = tx.description.match(/TX: ([a-fA-F0-9]+)/);
          const txid = txidMatch ? txidMatch[1] : null;
          
          return {
            id: tx.id,
            type: tx.type,
            amount: tx.amount,
            description: tx.description,
            status: tx.status,
            createdAt: tx.createdAt,
            txid: txid,
          };
        }

        return {
          id: tx.id,
          type: tx.type,
          amount: tx.amount,
          description: tx.description,
          status: tx.status,
          createdAt: tx.createdAt,
          reference: tx.reference,
        };
      });

    console.log('\nFormatted transactions (what API will return):');
    formattedTransactions.forEach((tx, index) => {
      console.log(`${index + 1}. ${tx.type} - $${tx.amount} (${tx.status})`);
      console.log(`   Description: ${tx.description}`);
      console.log(`   Created: ${tx.createdAt}`);
      if (tx.txid) console.log(`   TX ID: ${tx.txid}`);
      if (tx.reference) console.log(`   Reference: ${tx.reference}`);
      console.log('');
    });

    // Check for duplicates by description
    const descriptionCounts = new Map<string, number>();
    formattedTransactions.forEach(tx => {
      const key = `${tx.type}-${tx.description}`;
      descriptionCounts.set(key, (descriptionCounts.get(key) || 0) + 1);
    });

    console.log('Duplicate check:');
    let duplicatesFound = false;
    for (const [key, count] of descriptionCounts) {
      if (count > 1) {
        console.log(`❌ DUPLICATE: ${key} appears ${count} times`);
        duplicatesFound = true;
      }
    }

    if (!duplicatesFound) {
      console.log('✅ No duplicates found in API response');
    }

    // Compare with old logic (what was causing duplicates)
    console.log('\n--- Comparison with old logic ---');
    
    const depositRecords = await prisma.depositTransaction.findMany({
      where: { userId: testUser.id },
      take: 50,
    });

    const withdrawalRequests = await prisma.withdrawalRequest.findMany({
      where: { userId: testUser.id },
      take: 50,
    });

    console.log(`Old logic would have included:`);
    console.log(`- ${transactions.length} transactions from Transaction table`);
    console.log(`- ${depositRecords.length} deposits from DepositTransaction table`);
    console.log(`- ${withdrawalRequests.length} withdrawals from WithdrawalRequest table`);
    console.log(`Total: ${transactions.length + depositRecords.length + withdrawalRequests.length} entries (with duplicates)`);
    console.log(`New logic returns: ${formattedTransactions.length} entries (no duplicates)`);

    console.log('\n🎉 Transaction API test completed!');

  } catch (error) {
    console.error('❌ Error during API test:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testTransactionAPI()
  .then(() => {
    console.log('✅ Test completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
