import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verifyDuplicateFix() {
  console.log('🔍 Verifying duplicate transaction fix...\n');

  try {
    // Get all users with transactions
    const users = await prisma.user.findMany({
      where: {
        transactions: {
          some: {
            type: { in: ['DEPOSIT', 'WITHDRAWAL'] }
          }
        }
      },
      include: {
        transactions: {
          where: {
            type: { in: ['DEPOSIT', 'WITHDRAWAL'] }
          }
        }
      }
    });

    console.log(`Found ${users.length} users with deposit/withdrawal transactions\n`);

    for (const user of users) {
      console.log(`\n👤 User: ${user.email} (${user.id})`);
      console.log(`   Transactions in Transaction table: ${user.transactions.length}`);

      // Simulate the NEW API logic (what we fixed)
      const apiTransactions = user.transactions
        .filter(tx => tx.type !== 'ADMIN_CREDIT' && tx.type !== 'ADMIN_DEBIT')
        .map(tx => ({
          id: tx.id,
          type: tx.type,
          amount: tx.amount,
          description: tx.description,
          status: tx.status,
          createdAt: tx.createdAt,
        }));

      console.log(`   API will return: ${apiTransactions.length} transactions`);

      // Show what OLD API logic would have returned (with duplicates)
      const depositRecords = await prisma.depositTransaction.findMany({
        where: { userId: user.id }
      });

      const withdrawalRequests = await prisma.withdrawalRequest.findMany({
        where: { userId: user.id }
      });

      const oldApiTotal = user.transactions.length + depositRecords.length + withdrawalRequests.length;
      console.log(`   OLD API would return: ${oldApiTotal} entries (${depositRecords.length} deposit duplicates + ${withdrawalRequests.length} withdrawal duplicates)`);

      // Check for potential duplicates in the new logic
      const transactionsByType = new Map<string, any[]>();
      apiTransactions.forEach(tx => {
        const key = tx.type;
        if (!transactionsByType.has(key)) {
          transactionsByType.set(key, []);
        }
        transactionsByType.get(key)!.push(tx);
      });

      console.log('   Transaction breakdown:');
      for (const [type, txs] of transactionsByType) {
        console.log(`     - ${type}: ${txs.length} transactions`);
        
        if (type === 'DEPOSIT') {
          // Check if each deposit has a unique transaction ID
          const txIds = new Set();
          txs.forEach(tx => {
            const txidMatch = tx.description.match(/TX: ([a-fA-F0-9]+)/);
            if (txidMatch) {
              const txid = txidMatch[1];
              if (txIds.has(txid)) {
                console.log(`       ❌ DUPLICATE DEPOSIT TX ID: ${txid}`);
              } else {
                txIds.add(txid);
                console.log(`       ✅ Unique deposit: ${txid.substring(0, 8)}...`);
              }
            }
          });
        }

        if (type === 'WITHDRAWAL') {
          // Check if each withdrawal has a unique reference
          const refs = new Set();
          txs.forEach(tx => {
            if (tx.reference) {
              if (refs.has(tx.reference)) {
                console.log(`       ❌ DUPLICATE WITHDRAWAL REF: ${tx.reference}`);
              } else {
                refs.add(tx.reference);
                console.log(`       ✅ Unique withdrawal: ${tx.reference.substring(0, 8)}...`);
              }
            }
          });
        }
      }
    }

    // Overall summary
    console.log('\n\n📊 SUMMARY:');
    
    const totalTransactions = await prisma.transaction.count({
      where: {
        type: { in: ['DEPOSIT', 'WITHDRAWAL'] }
      }
    });

    const totalDepositRecords = await prisma.depositTransaction.count();
    const totalWithdrawalRequests = await prisma.withdrawalRequest.count();

    console.log(`Total transactions in Transaction table: ${totalTransactions}`);
    console.log(`Total deposit records in DepositTransaction table: ${totalDepositRecords}`);
    console.log(`Total withdrawal requests in WithdrawalRequest table: ${totalWithdrawalRequests}`);
    console.log('');
    console.log(`OLD API would show: ${totalTransactions + totalDepositRecords + totalWithdrawalRequests} entries (WITH DUPLICATES)`);
    console.log(`NEW API will show: ${totalTransactions} entries (NO DUPLICATES)`);
    console.log(`Duplicate reduction: ${totalDepositRecords + totalWithdrawalRequests} fewer entries`);

    // Check for any remaining duplicates in Transaction table itself
    console.log('\n🔍 Checking for duplicates within Transaction table...');
    
    const duplicateCheck = await prisma.transaction.groupBy({
      by: ['userId', 'type', 'description'],
      where: {
        type: { in: ['DEPOSIT', 'WITHDRAWAL'] }
      },
      _count: {
        id: true
      },
      having: {
        id: {
          _count: {
            gt: 1
          }
        }
      }
    });

    if (duplicateCheck.length === 0) {
      console.log('✅ No duplicates found within Transaction table');
    } else {
      console.log(`❌ Found ${duplicateCheck.length} duplicate groups within Transaction table:`);
      duplicateCheck.forEach(group => {
        console.log(`   - User ${group.userId}, ${group.type}: ${group._count.id} duplicates`);
      });
    }

    console.log('\n🎉 Duplicate fix verification completed!');

  } catch (error) {
    console.error('❌ Error during verification:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the verification
verifyDuplicateFix()
  .then(() => {
    console.log('✅ Verification completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Verification failed:', error);
    process.exit(1);
  });
