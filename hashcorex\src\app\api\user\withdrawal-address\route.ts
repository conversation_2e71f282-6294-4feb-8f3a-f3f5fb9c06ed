import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';

// GET - Get user's withdrawal address
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Use direct Prisma call to get user details
    const { prisma } = await import('@/lib/prisma');
    const userDetails = await prisma.user.findUnique({
      where: { email: user.email },
      select: {
        id: true,
        email: true,
        withdrawalAddress: true,
      },
    });

    if (!userDetails) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    const withdrawalAddress = userDetails.withdrawalAddress || '';

    return NextResponse.json({
      success: true,
      data: {
        withdrawalAddress,
      },
    });

  } catch (error: any) {
    console.error('Get withdrawal address error:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get withdrawal address',
        details: error.message
      },
      { status: 500 }
    );
  }
}

// POST - Update user's withdrawal address
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { withdrawalAddress } = body;

    // Validate USDT TRC20 address format if provided
    if (withdrawalAddress && !withdrawalAddress.match(/^T[A-Za-z1-9]{33}$/)) {
      return NextResponse.json(
        { success: false, error: 'Invalid USDT TRC20 address format' },
        { status: 400 }
      );
    }

    // Update user's withdrawal address using direct Prisma call
    try {
      console.log('Attempting to update withdrawal address for user:', user.email);
      console.log('New withdrawal address:', withdrawalAddress);

      // Use direct Prisma call instead of userDb method
      const { prisma } = await import('@/lib/prisma');
      const updatedUser = await prisma.user.update({
        where: { email: user.email },
        data: { withdrawalAddress: withdrawalAddress || null },
      });

      console.log('Successfully updated user:', updatedUser.id);
    } catch (dbError: any) {
      console.error('Database update error:', dbError);
      console.error('Error details:', dbError.message);
      console.error('Error code:', dbError.code);
      throw new Error(`Database error: ${dbError.message}`);
    }

    return NextResponse.json({
      success: true,
      message: 'Withdrawal address updated successfully',
    });

  } catch (error: any) {
    console.error('Update withdrawal address error:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update withdrawal address',
        details: error.message
      },
      { status: 500 }
    );
  }
}
