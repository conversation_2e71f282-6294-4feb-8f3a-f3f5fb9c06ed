import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { transactionDb, depositTransactionDb } from '@/lib/database';
import { prisma } from '@/lib/prisma';

// GET - Fetch user's transactions with search and filtering
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const search = searchParams.get('search') || '';
    const type = searchParams.get('type') || '';
    const status = searchParams.get('status') || '';
    const dateFrom = searchParams.get('dateFrom') || '';
    const dateTo = searchParams.get('dateTo') || '';

    // Build where clause for transactions
    const where: any = { userId: user.id };
    
    if (type && type !== 'ALL') {
      where.type = type;
    }
    
    if (status && status !== 'ALL') {
      where.status = status;
    }

    // Date filtering
    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        where.createdAt.lte = new Date(dateTo + 'T23:59:59.999Z');
      }
    }

    // Build filters for database query
    const dbFilters: any = {
      limit: Math.min(limit, 100),
      offset,
    };

    if (search) {
      dbFilters.search = search;
    }

    if (type && type !== 'ALL') {
      dbFilters.types = [type];
    }

    if (status && status !== 'ALL') {
      dbFilters.status = status;
    }

    // Get transactions with database-level filtering
    const transactions = await transactionDb.findByUserId(user.id, dbFilters);

    // We will ONLY use the Transaction table for display to avoid duplicates
    // The Transaction table should contain all completed transactions
    // DepositTransaction and WithdrawalRequest are for admin management only

    // Format transactions for display - only use Transaction table to avoid duplicates
    const allTransactions = transactions
      .filter(tx => tx.type !== 'ADMIN_CREDIT' && tx.type !== 'ADMIN_DEBIT') // Exclude admin transactions from user view
      .map(tx => {
        // For withdrawal transactions, get additional details from withdrawal request
        if (tx.type === 'WITHDRAWAL' && tx.reference) {
          // We'll fetch withdrawal details if needed for the modal
          return {
            id: tx.id,
            type: tx.type,
            category: 'TRANSACTION',
            amount: tx.amount,
            description: tx.description,
            status: tx.status,
            reference: tx.reference,
            createdAt: tx.createdAt,
            // Additional details for modal (will be populated when modal is opened)
            txid: null,
            usdtAddress: null,
            rejectionReason: null,
            processedAt: null,
          };
        }

        // For deposit transactions, extract transaction ID from description
        if (tx.type === 'DEPOSIT') {
          const txidMatch = tx.description.match(/TX: ([a-fA-F0-9]+)/);
          const txid = txidMatch ? txidMatch[1] : null;

          return {
            id: tx.id,
            type: tx.type,
            category: 'TRANSACTION',
            amount: tx.amount,
            description: tx.description,
            status: tx.status,
            reference: tx.reference,
            createdAt: tx.createdAt,
            // Additional details for modal
            txid: txid,
            usdtAddress: null,
            rejectionReason: null,
            processedAt: null,
          };
        }

        // For all other transaction types
        return {
          id: tx.id,
          type: tx.type,
          category: 'TRANSACTION',
          amount: tx.amount,
          description: tx.description,
          status: tx.status,
          reference: tx.reference,
          createdAt: tx.createdAt,
          // Additional details for modal
          txid: null,
          usdtAddress: null,
          rejectionReason: null,
          processedAt: null,
        };
      });

    // Sort by creation date (newest first)
    allTransactions.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    // Apply pagination to combined results
    const paginatedTransactions = allTransactions.slice(offset, offset + limit);

    // Get transaction type options for filtering (exclude admin types from user view)
    const transactionTypes = [
      'ALL',
      'MINING_EARNINGS',
      'DIRECT_REFERRAL',
      'BINARY_BONUS',
      'DEPOSIT',
      'WITHDRAWAL',
      'PURCHASE'
    ];

    const statusOptions = [
      'ALL',
      'PENDING',
      'COMPLETED',
      'FAILED',
      'CANCELLED',
      'CONFIRMED',
      'PENDING_VERIFICATION',
      'WAITING_FOR_CONFIRMATIONS'
    ];

    return NextResponse.json({
      success: true,
      data: {
        transactions: paginatedTransactions,
        pagination: {
          limit,
          offset,
          total: allTransactions.length,
          hasMore: offset + limit < allTransactions.length,
        },
        filters: {
          transactionTypes,
          statusOptions,
        },
      },
    });

  } catch (error: any) {
    console.error('Wallet transactions fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch wallet transactions' },
      { status: 500 }
    );
  }
}
