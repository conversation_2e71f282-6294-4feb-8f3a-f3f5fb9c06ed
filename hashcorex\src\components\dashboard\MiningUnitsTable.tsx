'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui';
import { MiningRig } from '@/components/icons';
import { Calendar, TrendingUp, DollarSign } from 'lucide-react';
import { formatCurrency, formatTHS, formatDateTime, formatNumber } from '@/lib/utils';
import { useClientOnly } from '@/hooks/useClientOnly';

interface MiningUnit {
  id: string;
  thsAmount: number;
  investmentAmount: number;
  startDate: string;
  expiryDate: string;
  dailyROI: number;
  totalEarned: number;
  miningEarnings: number;
  referralEarnings: number;
  binaryEarnings: number;
  status: 'ACTIVE' | 'EXPIRED';
  createdAt: string;
}

export const MiningUnitsTable: React.FC = () => {
  const [miningUnits, setMiningUnits] = useState<MiningUnit[]>([]);
  const [loading, setLoading] = useState(true);
  const isClient = useClientOnly();

  useEffect(() => {
    fetchMiningUnits();
  }, []);

  const fetchMiningUnits = async () => {
    try {
      const response = await fetch('/api/mining-units', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setMiningUnits(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch mining units:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateProgress = (unit: MiningUnit) => {
    const maxEarnings = unit.investmentAmount * 5;
    const totalEarnings = unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;
    const progress = (totalEarnings / maxEarnings) * 100;
    return Math.min(progress, 100);
  };

  const getTotalEarnings = (unit: MiningUnit) => {
    return unit.miningEarnings + unit.referralEarnings + unit.binaryEarnings;
  };

  const getRemainingCapacity = (unit: MiningUnit) => {
    const maxEarnings = unit.investmentAmount * 5;
    const totalEarnings = getTotalEarnings(unit);
    return Math.max(0, maxEarnings - totalEarnings);
  };

  const getExpirationOrder = () => {
    // Sort active units by creation date (FIFO order)
    return miningUnits
      .filter(unit => unit.status === 'ACTIVE')
      .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
      .map((unit, index) => ({ unitId: unit.id, order: index + 1 }));
  };

  const getDaysRemaining = (expiryDate: string) => {
    if (!isClient) return 0; // Return 0 on server to prevent hydration mismatch

    const expiry = new Date(expiryDate);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(diffDays, 0);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-eco-100 text-eco-700';
      case 'EXPIRED':
        return 'bg-gray-100 text-gray-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MiningRig className="h-5 w-5 text-solar-500" />
            <span>Mining Units</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MiningRig className="h-5 w-5 text-solar-500" />
          <span>Mining Units ({miningUnits.length})</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {miningUnits.length > 0 ? (
          <div className="space-y-4">
            {/* Desktop Table */}
            <div className="hidden md:block overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Order</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Mining Power</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Investment</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Earnings Breakdown</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Progress to 5x</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Remaining</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {miningUnits.map((unit) => {
                    const expirationOrder = getExpirationOrder();
                    const orderInfo = expirationOrder.find(o => o.unitId === unit.id);

                    return (
                      <tr key={unit.id} className="border-b border-gray-100 hover:bg-gray-50">
                        {/* FIFO Order */}
                        <td className="py-4 px-4">
                          {unit.status === 'ACTIVE' && orderInfo ? (
                            <div className="flex items-center space-x-2">
                              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                                #{orderInfo.order}
                              </span>
                              <span className="text-xs text-gray-500">Next to expire</span>
                            </div>
                          ) : (
                            <span className="text-gray-400 text-xs">-</span>
                          )}
                        </td>

                        {/* Mining Power */}
                        <td className="py-4 px-4">
                          <div className="flex items-center space-x-2">
                            <MiningRig className="h-4 w-4 text-solar-500" />
                            <div>
                              <span className="font-medium">{formatTHS(unit.thsAmount)}</span>
                              <div className="text-xs text-gray-500">
                                {formatNumber(unit.dailyROI, 2)}% daily
                              </div>
                            </div>
                          </div>
                        </td>

                        {/* Investment */}
                        <td className="py-4 px-4">
                          <span className="font-medium">{formatCurrency(unit.investmentAmount)}</span>
                        </td>

                        {/* Earnings Breakdown */}
                        <td className="py-4 px-4">
                          <div className="space-y-1">
                            <div className="flex justify-between text-xs">
                              <span className="text-gray-600">Mining:</span>
                              <span className="font-medium text-green-600">{formatCurrency(unit.miningEarnings)}</span>
                            </div>
                            <div className="flex justify-between text-xs">
                              <span className="text-gray-600">Referral:</span>
                              <span className="font-medium text-blue-600">{formatCurrency(unit.referralEarnings)}</span>
                            </div>
                            <div className="flex justify-between text-xs">
                              <span className="text-gray-600">Binary:</span>
                              <span className="font-medium text-purple-600">{formatCurrency(unit.binaryEarnings)}</span>
                            </div>
                            <div className="border-t pt-1 flex justify-between text-sm font-medium">
                              <span>Total:</span>
                              <span className="text-eco-600">{formatCurrency(getTotalEarnings(unit))}</span>
                            </div>
                          </div>
                        </td>

                        {/* Progress to 5x */}
                        <td className="py-4 px-4">
                          <div className="w-full bg-gray-200 rounded-full h-3">
                            <div
                              className="bg-eco-500 h-3 rounded-full transition-all duration-300"
                              style={{ width: `${calculateProgress(unit)}%` }}
                            ></div>
                          </div>
                          <div className="text-xs text-gray-500 mt-1 flex justify-between">
                            <span>{formatNumber(calculateProgress(unit), 1)}%</span>
                            <span>{formatCurrency(unit.investmentAmount * 5)} max</span>
                          </div>
                        </td>

                        {/* Remaining Capacity */}
                        <td className="py-4 px-4">
                          <div className="text-center">
                            <span className="font-medium text-gray-700">
                              {formatCurrency(getRemainingCapacity(unit))}
                            </span>
                            <div className="text-xs text-gray-500">
                              remaining
                            </div>
                          </div>
                        </td>

                        {/* Status */}
                        <td className="py-4 px-4">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(unit.status)}`}>
                            {unit.status}
                          </span>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {/* Mobile Cards */}
            <div className="md:hidden space-y-4">
              {miningUnits.map((unit) => {
                const expirationOrder = getExpirationOrder();
                const orderInfo = expirationOrder.find(o => o.unitId === unit.id);

                return (
                  <div key={unit.id} className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <MiningRig className="h-5 w-5 text-solar-500" />
                        <span className="font-semibold">{formatTHS(unit.thsAmount)}</span>
                        {unit.status === 'ACTIVE' && orderInfo && (
                          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                            #{orderInfo.order}
                          </span>
                        )}
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(unit.status)}`}>
                        {unit.status}
                      </span>
                    </div>

                    <div className="grid grid-cols-2 gap-4 mb-3">
                      <div>
                        <div className="text-xs text-gray-500">Investment</div>
                        <div className="font-medium">{formatCurrency(unit.investmentAmount)}</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-500">Daily ROI</div>
                        <div className="font-medium text-eco-600">{formatNumber(unit.dailyROI, 2)}%</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-500">Remaining</div>
                        <div className="font-medium text-orange-600">{formatCurrency(getRemainingCapacity(unit))}</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-500">Max Earnings</div>
                        <div className="font-medium">{formatCurrency(unit.investmentAmount * 5)}</div>
                      </div>
                    </div>

                    {/* Earnings Breakdown */}
                    <div className="mb-3 p-3 bg-white rounded-lg">
                      <div className="text-xs font-medium text-gray-700 mb-2">Earnings Breakdown</div>
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span className="text-gray-600">Mining:</span>
                          <span className="font-medium text-green-600">{formatCurrency(unit.miningEarnings)}</span>
                        </div>
                        <div className="flex justify-between text-xs">
                          <span className="text-gray-600">Referral:</span>
                          <span className="font-medium text-blue-600">{formatCurrency(unit.referralEarnings)}</span>
                        </div>
                        <div className="flex justify-between text-xs">
                          <span className="text-gray-600">Binary:</span>
                          <span className="font-medium text-purple-600">{formatCurrency(unit.binaryEarnings)}</span>
                        </div>
                        <div className="border-t pt-1 flex justify-between text-sm font-medium">
                          <span>Total:</span>
                          <span className="text-eco-600">{formatCurrency(getTotalEarnings(unit))}</span>
                        </div>
                      </div>
                    </div>

                    <div className="mb-2">
                      <div className="flex justify-between text-xs text-gray-500 mb-1">
                        <span>Progress to 5x</span>
                        <span>{formatNumber(calculateProgress(unit), 1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-3">
                        <div
                          className="bg-eco-500 h-3 rounded-full transition-all duration-300"
                          style={{ width: `${calculateProgress(unit)}%` }}
                        ></div>
                      </div>
                    </div>

                    <div className="text-xs text-gray-500">
                      Started: {formatDateTime(unit.startDate)} • Expires: {formatDateTime(unit.expiryDate)}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Summary */}
            <div className="mt-6 p-4 bg-solar-50 rounded-lg">
              <h4 className="font-medium text-dark-900 mb-3">Mining Units Summary</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Total Mining Power: </span>
                  <span className="font-medium">
                    {formatTHS(miningUnits.reduce((sum, unit) => sum + unit.thsAmount, 0))}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Total Investment: </span>
                  <span className="font-medium">
                    {formatCurrency(miningUnits.reduce((sum, unit) => sum + unit.investmentAmount, 0))}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Active Units: </span>
                  <span className="font-medium text-green-600">
                    {miningUnits.filter(unit => unit.status === 'ACTIVE').length}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Expired Units: </span>
                  <span className="font-medium text-red-600">
                    {miningUnits.filter(unit => unit.status === 'EXPIRED').length}
                  </span>
                </div>
              </div>

              {/* Earnings Breakdown Summary */}
              <div className="mt-4 pt-4 border-t border-gray-200">
                <h5 className="font-medium text-dark-900 mb-2">Total Earnings Breakdown</h5>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Mining Earnings: </span>
                    <span className="font-medium text-green-600">
                      {formatCurrency(miningUnits.reduce((sum, unit) => sum + unit.miningEarnings, 0))}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Referral Earnings: </span>
                    <span className="font-medium text-blue-600">
                      {formatCurrency(miningUnits.reduce((sum, unit) => sum + unit.referralEarnings, 0))}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Binary Earnings: </span>
                    <span className="font-medium text-purple-600">
                      {formatCurrency(miningUnits.reduce((sum, unit) => sum + unit.binaryEarnings, 0))}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Total Earned: </span>
                    <span className="font-medium text-eco-600">
                      {formatCurrency(miningUnits.reduce((sum, unit) => sum + getTotalEarnings(unit), 0))}
                    </span>
                  </div>
                </div>
              </div>

              {/* FIFO Information */}
              {miningUnits.filter(unit => unit.status === 'ACTIVE').length > 1 && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <h5 className="font-medium text-dark-900 mb-2">FIFO Expiration Order</h5>
                  <p className="text-xs text-gray-600 mb-2">
                    Mining units expire when they reach 5x their investment amount. Units expire in First-In-First-Out (FIFO) order.
                  </p>
                  <div className="text-sm">
                    <span className="text-gray-600">Next to expire: </span>
                    <span className="font-medium text-orange-600">
                      Unit #{getExpirationOrder()[0]?.order || 'N/A'}
                      ({formatNumber(calculateProgress(miningUnits.find(u => u.id === getExpirationOrder()[0]?.unitId) || miningUnits[0]), 1)}% complete)
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <MiningRig className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Mining Units</h3>
            <p className="text-gray-500 mb-4">
              You haven't purchased any mining units yet. Start mining to earn daily returns!
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
