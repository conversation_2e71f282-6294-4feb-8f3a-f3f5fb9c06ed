'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { DashboardOverview } from '@/components/dashboard/DashboardOverview';
import { PurchaseMiningUnit } from '@/components/dashboard/PurchaseMiningUnit';
import { EarningsTracker } from '@/components/dashboard/EarningsTracker';
import { WalletDashboard } from '@/components/dashboard/WalletDashboard';
import { D3BinaryTree } from '@/components/dashboard/D3BinaryTree';
import { KYCPortal } from '@/components/dashboard/KYCPortal';
import { MiningUnitsTable } from '@/components/dashboard/MiningUnitsTable';
import { SupportCenter } from '@/components/dashboard/SupportCenter';
import { UserProfileSettings } from '@/components/dashboard/UserProfileSettings';
import { Loading } from '@/components/ui';

export default function DashboardPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" text="Loading dashboard..." />
      </div>
    );
  }

  if (!user) {
    return null;
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <DashboardOverview onTabChange={setActiveTab} />;
      case 'mining':
        return (
          <div className="space-y-6">
            <PurchaseMiningUnit onPurchaseComplete={() => window.location.reload()} />
            <MiningUnitsTable />
          </div>
        );
      case 'earnings':
        return <EarningsTracker />;
      case 'wallet':
        return <WalletDashboard />;
      case 'referrals':
        return <D3BinaryTree />;
      case 'kyc':
        return <KYCPortal />;
      case 'support':
        return <SupportCenter />;
      case 'profile':
        return <UserProfileSettings />;
      default:
        return <DashboardOverview />;
    }
  };

  return (
    <DashboardLayout activeTab={activeTab} onTabChange={setActiveTab}>
      {renderTabContent()}
    </DashboardLayout>
  );
}
