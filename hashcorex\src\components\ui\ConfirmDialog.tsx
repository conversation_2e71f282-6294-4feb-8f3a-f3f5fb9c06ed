'use client';

import React from 'react';
import { createPortal } from 'react-dom';
import { AlertTriangle, CheckCircle, Info, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from './Button';

export interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string | React.ReactNode;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'danger' | 'warning' | 'success';
  darkMode?: boolean;
  loading?: boolean;
}

export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'default',
  darkMode = false,
  loading = false,
}) => {
  if (!isOpen) return null;

  const getIcon = () => {
    switch (variant) {
      case 'danger':
        return <AlertTriangle className="h-6 w-6 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-6 w-6 text-yellow-500" />;
      case 'success':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      default:
        return <Info className="h-6 w-6 text-blue-500" />;
    }
  };

  const getConfirmButtonVariant = () => {
    switch (variant) {
      case 'danger':
        return 'destructive';
      case 'warning':
        return 'warning';
      case 'success':
        return 'default';
      default:
        return 'default';
    }
  };

  const modalContent = (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Dialog */}
      <div
        className={cn(
          'relative w-full max-w-md rounded-xl shadow-xl transform transition-all',
          darkMode ? 'bg-slate-800 border border-slate-700' : 'bg-white'
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className={cn(
          'flex items-center justify-between p-6 border-b',
          darkMode ? 'border-slate-700' : 'border-gray-200'
        )}>
          <div className="flex items-center space-x-3">
            {getIcon()}
            <h2 className={cn(
              'text-lg font-semibold',
              darkMode ? 'text-white' : 'text-gray-900'
            )}>
              {title}
            </h2>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            disabled={loading}
            className="h-8 w-8 rounded-full"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Content */}
        <div className="p-6">
          {typeof message === 'string' ? (
            <p className={cn(
              'text-sm leading-relaxed',
              darkMode ? 'text-slate-300' : 'text-gray-600'
            )}>
              {message}
            </p>
          ) : (
            <div className={cn(
              'text-sm leading-relaxed',
              darkMode ? 'text-slate-300' : 'text-gray-600'
            )}>
              {message}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className={cn(
          'flex items-center justify-end space-x-3 p-6 border-t',
          darkMode ? 'border-slate-700' : 'border-gray-200'
        )}>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={loading}
            className={cn(
              darkMode ? 'border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white' : ''
            )}
          >
            {cancelText}
          </Button>
          <Button
            variant={getConfirmButtonVariant()}
            onClick={onConfirm}
            disabled={loading}
            className={cn(
              loading && 'opacity-50 cursor-not-allowed'
            )}
          >
            {loading ? 'Processing...' : confirmText}
          </Button>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

// Hook for easier usage
export const useConfirmDialog = () => {
  const [dialogState, setDialogState] = React.useState<{
    isOpen: boolean;
    title: string;
    message: string;
    onConfirm: () => void;
    variant?: 'default' | 'danger' | 'warning' | 'success';
    confirmText?: string;
    cancelText?: string;
    darkMode?: boolean;
  }>({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {},
  });

  const [loading, setLoading] = React.useState(false);

  const showConfirm = (options: {
    title: string;
    message: string;
    onConfirm: () => void | Promise<void>;
    variant?: 'default' | 'danger' | 'warning' | 'success';
    confirmText?: string;
    cancelText?: string;
    darkMode?: boolean;
  }) => {
    setDialogState({
      isOpen: true,
      ...options,
      onConfirm: async () => {
        setLoading(true);
        try {
          await options.onConfirm();
          setDialogState(prev => ({ ...prev, isOpen: false }));
        } catch (error) {
          console.error('Confirm action failed:', error);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const hideConfirm = () => {
    if (!loading) {
      setDialogState(prev => ({ ...prev, isOpen: false }));
    }
  };

  const ConfirmDialogComponent = () => (
    <ConfirmDialog
      isOpen={dialogState.isOpen}
      onClose={hideConfirm}
      onConfirm={dialogState.onConfirm}
      title={dialogState.title}
      message={dialogState.message}
      variant={dialogState.variant}
      confirmText={dialogState.confirmText}
      cancelText={dialogState.cancelText}
      darkMode={dialogState.darkMode}
      loading={loading}
    />
  );

  return {
    showConfirm,
    hideConfirm,
    ConfirmDialog: ConfirmDialogComponent,
    loading,
  };
};

export { ConfirmDialog as default };
