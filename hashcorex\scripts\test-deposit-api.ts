import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testDepositAPI() {
  console.log('🧪 Testing deposit API with actual transaction...\n');

  const testTxId = '0439d3658e5a6fd7febc91f807b60cccdeda39e06ac26a13acb80f6d1616b145';

  try {
    // First, let's check if this transaction already exists in the database
    console.log('🔍 Checking existing deposits...');
    const existingDeposit = await prisma.depositTransaction.findUnique({
      where: { transactionId: testTxId }
    });

    if (existingDeposit) {
      console.log('📋 Existing deposit found:');
      console.log(`   Status: ${existingDeposit.status}`);
      console.log(`   Amount: ${existingDeposit.usdtAmount} USDT`);
      console.log(`   Created: ${existingDeposit.createdAt}`);
      console.log(`   Updated: ${existingDeposit.updatedAt}`);

      // Delete the existing deposit to test fresh
      console.log('\n🗑️  Removing existing deposit for fresh test...');
      await prisma.depositTransaction.delete({
        where: { transactionId: testTxId }
      });
      console.log('✅ Existing deposit removed');
    }

    // Get a test user (first user in database)
    console.log('\n👤 Getting test user...');
    const testUser = await prisma.user.findFirst();
    if (!testUser) {
      console.log('❌ No users found in database. Please create a user first.');
      return;
    }
    console.log(`✅ Using test user: ${testUser.email} (ID: ${testUser.id})`);

    // Create a new deposit transaction record
    console.log('\n📝 Creating deposit transaction record...');
    const newDeposit = await prisma.depositTransaction.create({
      data: {
        transactionId: testTxId,
        userId: testUser.id,
        tronAddress: 'TXLEkeZHx4dtGG6gxEjKhwWRJYYxrmV9t8', // The correct deposit address
        status: 'PENDING_VERIFICATION',
        usdtAmount: 0, // Will be updated during verification
        amount: 0, // Will be updated during verification
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    });

    console.log('✅ Deposit transaction created:');
    console.log(`   ID: ${newDeposit.id}`);
    console.log(`   Transaction ID: ${newDeposit.transactionId}`);
    console.log(`   Status: ${newDeposit.status}`);
    // Trigger verification manually
    console.log('\n🔄 Triggering verification...');
    const { depositVerificationService } = await import('@/lib/depositVerificationService');

    await depositVerificationService.addTransactionForVerification(
      testTxId,
      'TXLEkeZHx4dtGG6gxEjKhwWRJYYxrmV9t8'
    );

    console.log('✅ Verification triggered');

    // Wait for verification to complete
    console.log('\n⏳ Waiting for verification to complete...');
    let attempts = 0;
    const maxAttempts = 30; // 30 seconds

    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      attempts++;

      const updatedDeposit = await prisma.depositTransaction.findUnique({
        where: { transactionId: testTxId }
      });

      if (updatedDeposit && updatedDeposit.status !== 'PENDING_VERIFICATION') {
        console.log(`\n🎉 Verification completed after ${attempts} seconds!`);
        console.log('📊 Final deposit status:');
        console.log(`   Status: ${updatedDeposit.status}`);
        console.log(`   Amount: ${updatedDeposit.usdtAmount} USDT`);
        console.log(`   Sender: ${updatedDeposit.senderAddress}`);
        console.log(`   Block: ${updatedDeposit.blockNumber}`);
        console.log(`   Confirmations: ${updatedDeposit.confirmations}`);
        console.log(`   Block Timestamp: ${updatedDeposit.blockTimestamp}`);

        if (updatedDeposit.status === 'COMPLETED') {
          console.log('\n✅ SUCCESS: Deposit was successfully verified and completed!');

          // Check if wallet balance was updated
          const walletBalance = await prisma.walletBalance.findUnique({
            where: { userId: testUser.id }
          });

          if (walletBalance) {
            console.log(`💰 User wallet balance: $${walletBalance.balance}`);
          }
        } else {
          console.log(`\n⚠️  Deposit status: ${updatedDeposit.status}`);
        }
        break;
      }

      if (attempts % 5 === 0) {
        console.log(`   Still waiting... (${attempts}/${maxAttempts})`);
      }
    }

    if (attempts >= maxAttempts) {
      console.log('\n⏰ Verification timeout - checking final status...');
      const finalDeposit = await prisma.depositTransaction.findUnique({
        where: { transactionId: testTxId }
      });

      if (finalDeposit) {
        console.log(`   Final status: ${finalDeposit.status}`);
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testDepositAPI();
