const CHUNK_PUBLIC_PATH = "server/app/api/referrals/tree/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_lib_referral_ts_9568ad5d._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__b5a0bc86._.js");
runtime.loadChunk("server/chunks/node_modules_396c14c4._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/referrals/tree/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/referrals/tree/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/referrals/tree/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
