'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent, Button } from '@/components/ui';
import { Grid, GridItem } from '@/components/layout';
import { MiningRig, Cryptocurrency, SolarPanel } from '@/components/icons';
import { TrendingUp, Wallet, Users, Zap, Clock, Award } from 'lucide-react';
import { formatCurrency, formatTHS, getTimeUntilNextPayout } from '@/lib/utils';

interface DashboardStats {
  totalTHS: number;
  estimatedEarnings: {
    next7Days: number;
    next30Days: number;
    next365Days: number;
  };
  walletBalance: number;
  pendingEarnings: number;
  activeUnits: number;
  totalEarnings: number;
  directReferrals: number;
  binaryPoints: {
    leftPoints: number;
    rightPoints: number;
  };
  miningUnits: {
    totalUnits: number;
    expiredUnits: number;
    nextToExpire: {
      id: string;
      progressPercentage: number;
      remainingCapacity: number;
      thsAmount: number;
    } | null;
    totalMiningEarnings: number;
    totalReferralEarnings: number;
    totalBinaryEarnings: number;
  };
}

interface DashboardOverviewProps {
  onTabChange?: (tab: string) => void;
}

export const DashboardOverview: React.FC<DashboardOverviewProps> = ({ onTabChange }) => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeUntilPayout, setTimeUntilPayout] = useState(getTimeUntilNextPayout());

  useEffect(() => {
    fetchDashboardStats();
    
    // Update countdown every second
    const interval = setInterval(() => {
      setTimeUntilPayout(getTimeUntilNextPayout());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const fetchDashboardStats = async () => {
    try {
      // Fetch data from multiple endpoints
      const [walletRes, earningsRes, miningRes, referralRes] = await Promise.all([
        fetch('/api/wallet/balance', { credentials: 'include' }),
        fetch('/api/earnings', { credentials: 'include' }),
        fetch('/api/mining-units', { credentials: 'include' }),
        fetch('/api/referrals/tree?depth=1', { credentials: 'include' }),
      ]);

      const [walletData, earningsData, miningData, referralData] = await Promise.all([
        walletRes.json(),
        earningsRes.json(),
        miningRes.json(),
        referralRes.json(),
      ]);

      if (walletData.success && earningsData.success && miningData.success && referralData.success) {
        const totalTHS = miningData.data.reduce((sum: number, unit: any) => sum + unit.thsAmount, 0);

        // Find the next unit to expire (active units sorted by creation date)
        const activeUnits = miningData.data.filter((unit: any) => unit.status === 'ACTIVE');
        const nextToExpire = activeUnits.length > 0 ? {
          id: activeUnits[0].id,
          progressPercentage: activeUnits[0].progressPercentage || 0,
          remainingCapacity: activeUnits[0].remainingCapacity || 0,
          thsAmount: activeUnits[0].thsAmount,
        } : null;

        setStats({
          totalTHS,
          estimatedEarnings: earningsData.data.estimatedEarnings,
          walletBalance: walletData.data.balance,
          pendingEarnings: walletData.data.pendingEarnings,
          activeUnits: miningData.data.length,
          totalEarnings: earningsData.data.totalEarnings,
          directReferrals: referralData.data.statistics.totalDirectReferrals,
          binaryPoints: referralData.data.statistics.binaryPoints,
          miningUnits: {
            totalUnits: miningData.summary?.totalUnits || miningData.data.length,
            expiredUnits: miningData.summary?.expiredUnits || 0,
            nextToExpire,
            totalMiningEarnings: miningData.summary?.totalMiningEarnings || 0,
            totalReferralEarnings: miningData.summary?.totalReferralEarnings || 0,
            totalBinaryEarnings: miningData.summary?.totalBinaryEarnings || 0,
          },
        });
      }
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-32 bg-gray-200 rounded-xl"></div>
          </div>
        ))}
      </div>
    );
  }

  if (!stats) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-gray-500">Failed to load dashboard data</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-green-600 rounded-2xl p-8 text-white shadow-lg">
        <h1 className="text-3xl font-bold mb-3">Welcome to HashCoreX</h1>
        <p className="text-green-100 text-lg leading-relaxed">
          Your sustainable mining dashboard. Track your earnings, manage your mining units, and grow your referral network.
        </p>
      </div>

      {/* Key Metrics */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Key Metrics</h2>
        <Grid cols={{ default: 1, sm: 2, lg: 4 }} gap={6}>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Total Mining Power</p>
                  <p className="text-3xl font-bold text-dark-900">
                    {formatTHS(stats.totalTHS)}
                  </p>
                </div>
                <div className="h-14 w-14 bg-solar-100 rounded-xl flex items-center justify-center">
                  <Zap className="h-7 w-7 text-solar-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Wallet Balance</p>
                  <p className="text-3xl font-bold text-eco-600">
                    {formatCurrency(stats.walletBalance)}
                  </p>
                </div>
                <div className="h-14 w-14 bg-eco-100 rounded-xl flex items-center justify-center">
                  <Wallet className="h-7 w-7 text-eco-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Total Earnings</p>
                  <p className="text-3xl font-bold text-dark-900">
                    {formatCurrency(stats.totalEarnings)}
                  </p>
                </div>
                <div className="h-14 w-14 bg-blue-100 rounded-xl flex items-center justify-center">
                  <TrendingUp className="h-7 w-7 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Direct Referrals</p>
                  <p className="text-3xl font-bold text-dark-900">
                    {stats.directReferrals}
                  </p>
                </div>
                <div className="h-14 w-14 bg-purple-100 rounded-xl flex items-center justify-center">
                  <Users className="h-7 w-7 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
      </div>

      {/* Earnings and Payout Section */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Earnings Overview</h2>
        <Grid cols={{ default: 1, lg: 2 }} gap={8}>
          {/* Estimated Earnings */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center space-x-3 text-lg">
                <div className="h-10 w-10 bg-eco-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="h-5 w-5 text-eco-600" />
                </div>
                <span>Estimated Earnings</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-5">
                <div className="flex justify-between items-center py-2">
                  <span className="text-gray-600 font-medium">Next 7 Days</span>
                  <span className="font-bold text-eco-600 text-lg">
                    {formatCurrency(stats.estimatedEarnings.next7Days)}
                  </span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-gray-600 font-medium">Next 30 Days</span>
                  <span className="font-bold text-eco-600 text-lg">
                    {formatCurrency(stats.estimatedEarnings.next30Days)}
                  </span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-gray-600 font-medium">Next 365 Days</span>
                  <span className="font-bold text-eco-600 text-lg">
                    {formatCurrency(stats.estimatedEarnings.next365Days)}
                  </span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-gray-600 font-medium">Next 2 Years</span>
                  <span className="font-bold text-eco-600 text-lg">
                    {formatCurrency(stats.estimatedEarnings.next2Years)}
                  </span>
                </div>
              </div>
              <div className="mt-6 p-4 bg-eco-50 rounded-xl">
                <p className="text-sm text-eco-700 font-medium">
                  * Based on current mining units and average ROI
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Next Payout */}
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center space-x-3 text-lg">
                <div className="h-10 w-10 bg-solar-100 rounded-lg flex items-center justify-center">
                  <Clock className="h-5 w-5 text-solar-600" />
                </div>
                <span>Next Payout</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-6 font-medium">
                  Weekly payout every Saturday at 15:00 UTC
                </p>
                <div className="grid grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="bg-solar-50 rounded-xl p-3 mb-2">
                      <div className="text-2xl font-bold text-solar-600">
                        {timeUntilPayout.days}
                      </div>
                    </div>
                    <div className="text-sm text-gray-600 font-medium">Days</div>
                  </div>
                  <div className="text-center">
                    <div className="bg-solar-50 rounded-xl p-3 mb-2">
                      <div className="text-2xl font-bold text-solar-600">
                        {timeUntilPayout.hours}
                      </div>
                    </div>
                    <div className="text-sm text-gray-600 font-medium">Hours</div>
                  </div>
                  <div className="text-center">
                    <div className="bg-solar-50 rounded-xl p-3 mb-2">
                      <div className="text-2xl font-bold text-solar-600">
                        {timeUntilPayout.minutes}
                      </div>
                    </div>
                    <div className="text-sm text-gray-600 font-medium">Min</div>
                  </div>
                  <div className="text-center">
                    <div className="bg-solar-50 rounded-xl p-3 mb-2">
                      <div className="text-2xl font-bold text-solar-600">
                        {timeUntilPayout.seconds}
                      </div>
                    </div>
                    <div className="text-sm text-gray-600 font-medium">Sec</div>
                  </div>
                </div>
                {stats.pendingEarnings > 0 && (
                  <div className="mt-6 p-4 bg-solar-50 rounded-xl">
                    <p className="text-sm text-solar-700 font-semibold">
                      <strong className="text-lg">{formatCurrency(stats.pendingEarnings)}</strong> pending
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </Grid>
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h2>
        <Card>
          <CardContent className="p-8">
            <Grid cols={{ default: 1, sm: 2, lg: 3 }} gap={6}>
              <Button
                className="h-20 flex flex-col items-center justify-center space-y-3 text-base font-semibold rounded-xl bg-green-600 text-white border-0"
                onClick={() => onTabChange?.('mining')}
              >
                <MiningRig className="h-7 w-7" />
                <span>Buy Mining Power</span>
              </Button>
              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center space-y-3 text-base font-semibold rounded-xl border-2 border-green-500 text-green-600"
                onClick={() => onTabChange?.('wallet')}
              >
                <Cryptocurrency className="h-7 w-7" />
                <span>Withdraw USDT</span>
              </Button>
              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center space-y-3 text-base font-semibold rounded-xl border-2 border-green-500 text-green-600"
                onClick={() => onTabChange?.('referrals')}
              >
                <Users className="h-7 w-7" />
                <span>Build Network</span>
              </Button>
            </Grid>
          </CardContent>
        </Card>
      </div>

      {/* FIFO Mining Units Summary */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Mining Units Overview</h2>
        <Grid cols={{ default: 1, lg: 2 }} gap={8}>
          {/* Earnings Breakdown */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center space-x-3 text-lg">
                <div className="h-10 w-10 bg-eco-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="h-5 w-5 text-eco-600" />
                </div>
                <span>Earnings Breakdown</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">Mining Earnings</span>
                  <span className="font-bold text-green-600">
                    {formatCurrency(stats.miningUnits.totalMiningEarnings)}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">Referral Earnings</span>
                  <span className="font-bold text-blue-600">
                    {formatCurrency(stats.miningUnits.totalReferralEarnings)}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">Binary Earnings</span>
                  <span className="font-bold text-purple-600">
                    {formatCurrency(stats.miningUnits.totalBinaryEarnings)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* FIFO Expiration Info */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center space-x-3 text-lg">
                <div className="h-10 w-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Clock className="h-5 w-5 text-orange-600" />
                </div>
                <span>FIFO Expiration System</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-4">
                <div className="p-4 bg-orange-50 rounded-lg">
                  <div className="text-sm text-gray-600 mb-2">Units expire at 5x investment using FIFO order</div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Total Units:</span>
                      <span className="font-medium ml-2">{stats.miningUnits.totalUnits}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Expired:</span>
                      <span className="font-medium ml-2 text-red-600">{stats.miningUnits.expiredUnits}</span>
                    </div>
                  </div>
                </div>

                {stats.miningUnits.nextToExpire ? (
                  <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                    <div className="text-sm font-medium text-gray-700 mb-2">Next to Expire (FIFO #1)</div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Mining Power:</span>
                        <span className="font-medium">{formatTHS(stats.miningUnits.nextToExpire.thsAmount)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Progress:</span>
                        <span className="font-medium text-orange-600">
                          {stats.miningUnits.nextToExpire.progressPercentage.toFixed(1)}%
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Remaining:</span>
                        <span className="font-medium text-green-600">
                          {formatCurrency(stats.miningUnits.nextToExpire.remainingCapacity)}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div
                          className="bg-orange-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${Math.min(stats.miningUnits.nextToExpire.progressPercentage, 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="p-4 bg-gray-50 rounded-lg text-center">
                    <div className="text-sm text-gray-500">No active mining units</div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </Grid>
      </div>

      {/* Binary Points Summary */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Binary Network Summary</h2>
        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center space-x-3 text-lg">
              <div className="h-10 w-10 bg-solar-100 rounded-lg flex items-center justify-center">
                <Award className="h-5 w-5 text-solar-600" />
              </div>
              <span>Network Performance</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <Grid cols={{ default: 1, sm: 3 }} gap={8}>
              <div className="text-center">
                <div className="bg-solar-50 rounded-xl p-6 mb-3">
                  <div className="text-3xl font-bold text-solar-600">
                    {stats.binaryPoints.leftPoints}
                  </div>
                </div>
                <div className="text-sm text-gray-600 font-medium">Left Points</div>
              </div>
              <div className="text-center">
                <div className="bg-solar-50 rounded-xl p-6 mb-3">
                  <div className="text-3xl font-bold text-solar-600">
                    {stats.binaryPoints.rightPoints}
                  </div>
                </div>
                <div className="text-sm text-gray-600 font-medium">Right Points</div>
              </div>
              <div className="text-center">
                <div className="bg-eco-50 rounded-xl p-6 mb-3">
                  <div className="text-3xl font-bold text-eco-600">
                    {Math.min(stats.binaryPoints.leftPoints, stats.binaryPoints.rightPoints)}
                  </div>
                </div>
                <div className="text-sm text-gray-600 font-medium">Potential Match</div>
              </div>
            </Grid>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
