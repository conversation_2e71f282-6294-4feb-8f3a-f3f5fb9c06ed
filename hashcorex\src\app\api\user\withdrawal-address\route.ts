import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { userDb } from '@/lib/database';

// GET - Get user's withdrawal address
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const userDetails = await userDb.findByEmail(user.email);

    if (!userDetails) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Handle case where withdrawalAddress field might not exist yet
    const withdrawalAddress = (userDetails as any).withdrawalAddress || '';

    return NextResponse.json({
      success: true,
      data: {
        withdrawalAddress,
      },
    });

  } catch (error: any) {
    console.error('Get withdrawal address error:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get withdrawal address',
        details: error.message
      },
      { status: 500 }
    );
  }
}

// POST - Update user's withdrawal address
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { withdrawalAddress } = body;

    // Validate USDT TRC20 address format if provided
    if (withdrawalAddress && !withdrawalAddress.match(/^T[A-Za-z1-9]{33}$/)) {
      return NextResponse.json(
        { success: false, error: 'Invalid USDT TRC20 address format' },
        { status: 400 }
      );
    }

    // Update user's withdrawal address
    try {
      await userDb.updateWithdrawalAddress(user.email, withdrawalAddress || null);
    } catch (dbError: any) {
      console.error('Database update error:', dbError);
      throw new Error(`Database error: ${dbError.message}`);
    }

    return NextResponse.json({
      success: true,
      message: 'Withdrawal address updated successfully',
    });

  } catch (error: any) {
    console.error('Update withdrawal address error:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update withdrawal address',
        details: error.message
      },
      { status: 500 }
    );
  }
}
