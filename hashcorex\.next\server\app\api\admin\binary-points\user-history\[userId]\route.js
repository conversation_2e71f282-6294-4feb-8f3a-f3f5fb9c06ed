const CHUNK_PUBLIC_PATH = "server/app/api/admin/binary-points/user-history/[userId]/route.js";
const runtime = require("../../../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_lib_referral_ts_0957f2f9._.js");
runtime.loadChunk("server/chunks/node_modules_2a1beb53._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__20ea19b1._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/admin/binary-points/user-history/[userId]/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/binary-points/user-history/[userId]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/binary-points/user-history/[userId]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
