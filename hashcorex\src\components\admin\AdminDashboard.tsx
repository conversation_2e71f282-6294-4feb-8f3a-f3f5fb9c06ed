'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent, useConfirmDialog } from '@/components/ui';
import { Grid } from '@/components/layout';
import {
  Users,
  DollarSign,
  TrendingUp,
  Zap,
  Shield,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  Play
} from 'lucide-react';
import { formatCurrency, formatNumber, formatTHS } from '@/lib/utils';

interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  pendingKYC: number;
  approvedKYC: number;
  totalInvestments: number;
  totalEarningsDistributed: number;
  totalTHSSold: number;
  activeTHS: number;
  pendingWithdrawals: number;
  totalWithdrawals: number;
}

interface AdminDashboardProps {
  onTabChange?: (tab: string) => void;
}

export const AdminDashboard: React.FC<AdminDashboardProps> = ({ onTabChange }) => {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [processingMatching, setProcessingMatching] = useState(false);
  const { showConfirm, ConfirmDialog } = useConfirmDialog();

  useEffect(() => {
    fetchAdminStats();
  }, []);

  const fetchAdminStats = async () => {
    try {
      const response = await fetch('/api/admin/stats', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setStats(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch admin stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleManualBinaryMatching = () => {
    showConfirm({
      title: 'Manual Binary Matching',
      message: 'Are you sure you want to manually trigger binary matching? This will process all users with binary points and distribute earnings.',
      variant: 'warning',
      confirmText: 'Process Matching',
      darkMode: true,
      onConfirm: async () => {
        setProcessingMatching(true);
        try {
          const response = await fetch('/api/admin/binary-matching/manual', {
            method: 'POST',
            credentials: 'include',
          });

          const data = await response.json();

          if (data.success) {
            // Show detailed results
            const results = data.data.matchingResults || [];
            const totalUsers = data.data.usersProcessed || 0;
            const totalPayouts = data.data.totalPayouts || 0;

            let resultsMessage = `Binary matching completed successfully!\n\n`;
            resultsMessage += `📊 SUMMARY:\n`;
            resultsMessage += `• Users processed: ${totalUsers}\n`;
            resultsMessage += `• Total payouts: $${totalPayouts.toFixed(2)}\n`;
            resultsMessage += `• Total matched points: ${results.reduce((sum, r) => sum + r.matchedPoints, 0).toFixed(2)}\n\n`;

            if (results.length > 0) {
              resultsMessage += `💰 DETAILED RESULTS:\n`;
              results.slice(0, 10).forEach((result, index) => {
                resultsMessage += `${index + 1}. User ${result.userId.substring(0, 8)}...\n`;
                resultsMessage += `   • Matched: ${result.matchedPoints.toFixed(2)} points\n`;
                resultsMessage += `   • Payout: $${result.payout.toFixed(2)}\n`;
                resultsMessage += `   • Remaining: L:${result.remainingLeftPoints.toFixed(2)} | R:${result.remainingRightPoints.toFixed(2)}\n\n`;
              });

              if (results.length > 10) {
                resultsMessage += `... and ${results.length - 10} more users\n\n`;
              }
            }

            resultsMessage += `✅ All earnings have been credited to user wallets.`;

            showConfirm({
              title: 'Binary Matching Results',
              message: resultsMessage,
              variant: 'success',
              confirmText: 'OK',
              cancelText: '',
              darkMode: true,
              onConfirm: () => {},
            });
            // Refresh stats to show updated data
            fetchAdminStats();
          } else {
            showConfirm({
              title: 'Error',
              message: `Error: ${data.error}`,
              variant: 'danger',
              confirmText: 'OK',
              cancelText: '',
              darkMode: true,
              onConfirm: () => {},
            });
          }
        } catch (error) {
          console.error('Manual binary matching error:', error);
          showConfirm({
            title: 'Error',
            message: 'Failed to process binary matching. Please try again.',
            variant: 'danger',
            confirmText: 'OK',
            cancelText: '',
            darkMode: true,
            onConfirm: () => {},
          });
        } finally {
          setProcessingMatching(false);
        }
      },
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-32 bg-slate-700 rounded-xl"></div>
          </div>
        ))}
      </div>
    );
  }

  if (!stats) {
    return (
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="text-center py-8">
          <p className="text-slate-400">Failed to load admin statistics</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* User Statistics */}
      <div>
        <h2 className="text-lg font-semibold text-white mb-4">User Management</h2>
        <Grid cols={{ default: 1, md: 2, lg: 4 }} gap={6}>
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Total Users</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {formatNumber(stats.totalUsers, 0)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <Users className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Active Users</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {formatNumber(stats.activeUsers, 0)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Pending KYC</p>
                  <p className="text-2xl font-bold text-orange-400">
                    {formatNumber(stats.pendingKYC, 0)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center">
                  <Shield className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Approved KYC</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {formatNumber(stats.approvedKYC, 0)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <Shield className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
      </div>

      {/* Financial Statistics */}
      <div>
        <h2 className="text-lg font-semibold text-white mb-4">Financial Overview</h2>
        <Grid cols={{ default: 1, md: 2, lg: 4 }} gap={6}>
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Total Investments</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {formatCurrency(stats.totalInvestments)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Earnings Distributed</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {formatCurrency(stats.totalEarningsDistributed)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>




        </Grid>
      </div>

      {/* Mining Statistics */}
      <div>
        <h2 className="text-lg font-semibold text-white mb-4">Mining Operations</h2>
        <Grid cols={{ default: 1, md: 2 }} gap={6}>
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Total TH/s Sold</p>
                  <p className="text-2xl font-bold text-orange-400">
                    {formatTHS(stats.totalTHSSold)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center">
                  <Zap className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Active TH/s</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {formatTHS(stats.activeTHS)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <Zap className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
      </div>

      {/* Withdrawal Management */}
      <div>
        <h2 className="text-lg font-semibold text-white mb-4">Withdrawal Management</h2>
        <Grid cols={{ default: 1, md: 2 }} gap={6}>
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Pending Withdrawals</p>
                  <p className="text-2xl font-bold text-red-400">
                    {formatNumber(stats.pendingWithdrawals, 0)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-red-600 rounded-full flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400">Total Withdrawals</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {formatCurrency(stats.totalWithdrawals)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <CreditCard className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
      </div>

      {/* Quick Actions */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <Grid cols={{ default: 1, md: 3 }} gap={4}>
            <div
              className="p-4 border border-slate-600 rounded-lg cursor-pointer"
              onClick={() => onTabChange?.('kyc')}
            >
              <div className="flex items-center space-x-3">
                <Shield className="h-8 w-8 text-orange-400" />
                <div>
                  <h3 className="font-medium text-white">Review KYC</h3>
                  <p className="text-sm text-slate-400">{stats.pendingKYC} pending reviews</p>
                </div>
              </div>
            </div>

            <div
              className="p-4 border border-slate-600 rounded-lg cursor-pointer"
              onClick={() => onTabChange?.('withdrawals')}
            >
              <div className="flex items-center space-x-3">
                <CreditCard className="h-8 w-8 text-red-400" />
                <div>
                  <h3 className="font-medium text-white">Process Withdrawals</h3>
                  <p className="text-sm text-slate-400">{stats.pendingWithdrawals} pending</p>
                </div>
              </div>
            </div>

            <div
              className="p-4 border border-slate-600 rounded-lg cursor-pointer"
              onClick={() => onTabChange?.('users')}
            >
              <div className="flex items-center space-x-3">
                <Users className="h-8 w-8 text-blue-400" />
                <div>
                  <h3 className="font-medium text-white">Manage Users</h3>
                  <p className="text-sm text-slate-400">{stats.totalUsers} total users</p>
                </div>
              </div>
            </div>

            <div
              className={`p-4 border border-slate-600 rounded-lg cursor-pointer ${processingMatching ? 'opacity-50 cursor-not-allowed' : ''}`}
              onClick={processingMatching ? undefined : handleManualBinaryMatching}
            >
              <div className="flex items-center space-x-3">
                <Play className="h-8 w-8 text-green-400" />
                <div>
                  <h3 className="font-medium text-white">
                    {processingMatching ? 'Processing...' : 'Manual Binary Matching'}
                  </h3>
                  <p className="text-sm text-slate-400">
                    {processingMatching ? 'Please wait...' : 'Trigger binary matching manually'}
                  </p>
                </div>
              </div>
            </div>
          </Grid>
        </CardContent>
      </Card>

      {/* Confirmation Dialog */}
      <ConfirmDialog />
    </div>
  );
};
