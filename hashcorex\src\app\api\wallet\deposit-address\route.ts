import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { adminSettingsDb } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the admin-configured deposit address - try both camelCase and UPPER_CASE keys for compatibility
    let depositAddress = await adminSettingsDb.get('usdtDepositAddress');
    if (!depositAddress) {
      depositAddress = await adminSettingsDb.get('USDT_DEPOSIT_ADDRESS');
    }

    // Clean deposit address - remove quotes and extra characters
    if (depositAddress) {
      depositAddress = depositAddress.replace(/['"]/g, '').trim();
    }

    if (!depositAddress) {
      return NextResponse.json(
        { success: false, error: 'Deposit address not configured. Please contact support.' },
        { status: 503 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        address: depositAddress,
        network: 'TRC20',
        currency: 'USDT',
      },
    });

  } catch (error) {
    console.error('Deposit address error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get deposit address' },
      { status: 500 }
    );
  }
}
