import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixWithdrawalStatus() {
  console.log('🔧 Fixing withdrawal status synchronization...\n');

  try {
    // Find the specific problematic withdrawal
    const problematicWithdrawal = await prisma.withdrawalRequest.findUnique({
      where: { id: 'cmcf5tdh1000hpglg5avlzuv3' },
      include: {
        user: {
          select: { email: true }
        }
      }
    });

    if (!problematicWithdrawal) {
      console.log('❌ Problematic withdrawal not found');
      return;
    }

    console.log(`Found problematic withdrawal:`);
    console.log(`   ID: ${problematicWithdrawal.id}`);
    console.log(`   User: ${problematicWithdrawal.user.email}`);
    console.log(`   Status: ${problematicWithdrawal.status}`);
    console.log(`   Amount: $${problematicWithdrawal.amount}`);

    // Find the related transaction
    const relatedTransaction = await prisma.transaction.findFirst({
      where: {
        reference: problematicWithdrawal.id,
        type: 'WITHDRAWAL'
      }
    });

    if (!relatedTransaction) {
      console.log('❌ Related transaction not found');
      return;
    }

    console.log(`\nFound related transaction:`);
    console.log(`   ID: ${relatedTransaction.id}`);
    console.log(`   Status: ${relatedTransaction.status}`);
    console.log(`   Amount: $${relatedTransaction.amount}`);

    // Fix the transaction status
    if (problematicWithdrawal.status === 'REJECTED' && relatedTransaction.status !== 'FAILED') {
      console.log(`\n🔧 Fixing transaction status from ${relatedTransaction.status} to FAILED...`);

      const updatedTransaction = await prisma.transaction.update({
        where: { id: relatedTransaction.id },
        data: { status: 'FAILED' }
      });

      console.log(`✅ Transaction status updated successfully`);
      console.log(`   New status: ${updatedTransaction.status}`);
    } else {
      console.log(`✅ Transaction status is already correct`);
    }

    // Check for any other mismatched withdrawals
    console.log('\n🔍 Checking for other status mismatches...');
    
    const allWithdrawals = await prisma.withdrawalRequest.findMany({
      include: {
        user: {
          select: { email: true }
        }
      }
    });

    let fixedCount = 0;

    for (const withdrawal of allWithdrawals) {
      const transaction = await prisma.transaction.findFirst({
        where: {
          reference: withdrawal.id,
          type: 'WITHDRAWAL'
        }
      });

      if (transaction) {
        const expectedStatus = withdrawal.status === 'REJECTED' ? 'FAILED' :
                             withdrawal.status === 'APPROVED' ? 'COMPLETED' :
                             withdrawal.status === 'COMPLETED' ? 'COMPLETED' : 'PENDING';

        if (transaction.status !== expectedStatus) {
          console.log(`🔧 Fixing withdrawal ${withdrawal.id} (${withdrawal.user.email})`);
          console.log(`   Withdrawal status: ${withdrawal.status}`);
          console.log(`   Transaction status: ${transaction.status} → ${expectedStatus}`);

          await prisma.transaction.update({
            where: { id: transaction.id },
            data: { status: expectedStatus }
          });

          fixedCount++;
          console.log(`   ✅ Fixed`);
        }
      }
    }

    console.log(`\n🎉 Status synchronization completed!`);
    console.log(`   Fixed ${fixedCount} mismatched transactions`);

  } catch (error) {
    console.error('❌ Error during fix:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
fixWithdrawalStatus()
  .then(() => {
    console.log('✅ Fix completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Fix failed:', error);
    process.exit(1);
  });
