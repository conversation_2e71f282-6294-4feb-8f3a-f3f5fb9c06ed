import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Fetch admin dashboard statistics
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Fetch all statistics in parallel
    const [
      userStats,
      kycStats,
      miningStats,
      transactionStats,
      withdrawalStats,
    ] = await Promise.all([
      // User statistics
      prisma.$transaction([
        prisma.user.count(),
        prisma.user.count({ where: { isActive: true } }),
      ]),
      
      // KYC statistics
      prisma.$transaction([
        // Count users who have submitted KYC documents and are pending review
        prisma.user.count({
          where: {
            kycStatus: 'PENDING',
            kycDocuments: {
              some: {} // Only count users who have uploaded at least one document
            }
          }
        }),
        prisma.user.count({ where: { kycStatus: 'APPROVED' } }),
      ]),
      
      // Mining statistics
      prisma.$transaction([
        prisma.miningUnit.aggregate({
          _sum: { thsAmount: true, investmentAmount: true },
        }),
        prisma.miningUnit.aggregate({
          where: { status: 'ACTIVE' },
          _sum: { thsAmount: true },
        }),
      ]),
      
      // Transaction statistics
      prisma.$transaction([
        prisma.transaction.aggregate({
          where: { 
            type: { in: ['MINING_EARNINGS', 'DIRECT_REFERRAL', 'BINARY_BONUS'] },
            status: 'COMPLETED'
          },
          _sum: { amount: true },
        }),
        prisma.transaction.aggregate({
          where: { type: 'PURCHASE', status: 'COMPLETED' },
          _sum: { amount: true },
        }),
      ]),
      
      // Withdrawal statistics
      prisma.$transaction([
        prisma.withdrawalRequest.count({ where: { status: 'PENDING' } }),
        prisma.withdrawalRequest.aggregate({
          where: { status: 'COMPLETED' },
          _sum: { amount: true },
        }),
      ]),
    ]);

    const totalInvestments = transactionStats[1]._sum.amount || 0;



    const stats = {
      // User statistics
      totalUsers: userStats[0],
      activeUsers: userStats[1],

      // KYC statistics
      pendingKYC: kycStats[0],
      approvedKYC: kycStats[1],

      // Financial statistics
      totalInvestments: totalInvestments,
      totalEarningsDistributed: transactionStats[0]._sum.amount || 0,

      // Mining statistics
      totalTHSSold: miningStats[0]._sum.thsAmount || 0,
      activeTHS: miningStats[1]._sum.thsAmount || 0,

      // Withdrawal statistics
      pendingWithdrawals: withdrawalStats[0],
      totalWithdrawals: withdrawalStats[1]._sum.amount || 0,
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });

  } catch (error: any) {
    console.error('Admin stats fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch admin statistics' },
      { status: 500 }
    );
  }
}
