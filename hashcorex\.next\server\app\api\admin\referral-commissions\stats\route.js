const CHUNK_PUBLIC_PATH = "server/app/api/admin/referral-commissions/stats/route.js";
const runtime = require("../../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_lib_referral_ts_be961771._.js");
runtime.loadChunk("server/chunks/node_modules_8d53ec31._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__5609cbb1._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/admin/referral-commissions/stats/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/referral-commissions/stats/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/referral-commissions/stats/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
