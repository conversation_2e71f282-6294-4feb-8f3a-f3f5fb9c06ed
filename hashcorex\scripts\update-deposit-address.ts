import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function updateDepositAddress() {
  console.log('🔧 Updating Deposit Address...\n');

  try {
    // The actual recipient address from the test transaction
    const correctDepositAddress = 'TXLEkeZHx4dtGG6gxEjKhwWRJYYxrmV9t8';

    console.log('📋 Current Settings Check:');

    // Check current deposit address settings
    const currentSettings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: ['usdtDepositAddress', 'USDT_DEPOSIT_ADDRESS']
        }
      }
    });

    console.log('Current deposit address settings:');
    currentSettings.forEach(setting => {
      console.log(`   ${setting.key}: ${setting.value}`);
    });

    console.log('\n🔄 Updating deposit address...');

    // Update both camelCase and UPPER_CASE versions for compatibility
    await prisma.adminSettings.upsert({
      where: { key: 'usdtDepositAddress' },
      update: { value: correctDepositAddress },
      create: { key: 'usdtDepositAddress', value: correctDepositAddress },
    });

    await prisma.adminSettings.upsert({
      where: { key: 'USDT_DEPOSIT_ADDRESS' },
      update: { value: correctDepositAddress },
      create: { key: 'USDT_DEPOSIT_ADDRESS', value: correctDepositAddress },
    });

    console.log(`✅ Deposit address updated to: ${correctDepositAddress}`);
    console.log('   This matches the recipient address in the test transaction.');
    console.log('   Users can now successfully verify deposits to this address.\n');

    // Verify the update
    const updatedSettings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: ['usdtDepositAddress', 'USDT_DEPOSIT_ADDRESS']
        }
      }
    });

    console.log('📊 Updated Settings:');
    updatedSettings.forEach(setting => {
      console.log(`   ${setting.key}: ${setting.value}`);
    });

    console.log('\n🎯 Transaction Details:');
    console.log('   Transaction ID: 0439d3658e5a6fd7febc91f807b60cccdeda39e06ac26a13acb80f6d1616b145');
    console.log('   Amount: 100 USDT');
    console.log('   From: TYBDwZs1sSbT2bFgBCr1efqWw62JHroDK7');
    console.log(`   To: ${correctDepositAddress}`);
    console.log('   Status: SUCCESS');
    console.log('   Confirmations: 195+ (well above required 10)');
    console.log('\n✨ The deposit system should now detect this transaction successfully!');

    // Also verify the network settings
    const networkSettings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: ['tronNetwork', 'tronApiUrl', 'tronUsdtContract']
        }
      }
    });

    console.log('📋 Current Network Settings:');
    networkSettings.forEach(setting => {
      console.log(`   ${setting.key}: ${setting.value}`);
    });

    if (networkSettings.length === 0) {
      console.log('⚠️  Network settings not found. Run init-tron-network-settings.ts first.');
    }

  } catch (error) {
    console.error('❌ Error updating deposit address:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateDepositAddress();
