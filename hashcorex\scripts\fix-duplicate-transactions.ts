import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixDuplicateTransactions() {
  console.log('🔧 Starting duplicate transaction cleanup...\n');

  try {
    // 1. Fix duplicate deposit transactions
    console.log('1. Fixing duplicate deposit transactions...');
    
    const duplicateDeposits = await prisma.transaction.findMany({
      where: {
        type: 'DEPOSIT',
        description: {
          contains: 'USDT TRC20 Deposit - TX:'
        }
      },
      orderBy: [
        { userId: 'asc' },
        { description: 'asc' },
        { createdAt: 'asc' }
      ]
    });

    console.log(`Found ${duplicateDeposits.length} deposit transactions`);

    // Group by user and transaction ID
    const groupedDeposits = new Map<string, typeof duplicateDeposits>();
    
    duplicateDeposits.forEach(tx => {
      const key = `${tx.userId}-${tx.description}`;
      if (!groupedDeposits.has(key)) {
        groupedDeposits.set(key, []);
      }
      groupedDeposits.get(key)!.push(tx);
    });

    let depositDuplicatesRemoved = 0;
    let depositAmountAdjusted = 0;

    for (const [key, transactions] of groupedDeposits) {
      if (transactions.length > 1) {
        console.log(`\n   Found ${transactions.length} duplicate deposits for: ${key}`);
        
        // Keep the first transaction (oldest), remove the rest
        const [keepTransaction, ...removeTransactions] = transactions;
        console.log(`   Keeping transaction: ${keepTransaction.id} (${keepTransaction.createdAt}) - Amount: ${keepTransaction.amount}`);

        for (const tx of removeTransactions) {
          console.log(`   Removing transaction: ${tx.id} (${tx.createdAt}) - Amount: ${tx.amount}`);
          
          // Remove the duplicate transaction
          await prisma.transaction.delete({
            where: { id: tx.id }
          });

          // Adjust wallet balance (subtract the duplicate amount)
          const wallet = await prisma.walletBalance.findUnique({
            where: { userId: tx.userId }
          });

          if (wallet) {
            await prisma.walletBalance.update({
              where: { userId: tx.userId },
              data: {
                availableBalance: wallet.availableBalance - tx.amount,
                totalDeposits: wallet.totalDeposits - tx.amount,
                lastUpdated: new Date(),
              }
            });

            console.log(`   Adjusted wallet balance: -${tx.amount} USDT`);
            depositAmountAdjusted += tx.amount;
          }

          depositDuplicatesRemoved++;
        }
      }
    }

    console.log(`\n✅ Deposit cleanup completed!`);
    console.log(`   Removed ${depositDuplicatesRemoved} duplicate deposit transactions`);
    console.log(`   Adjusted wallet balances by -${depositAmountAdjusted} USDT total`);

    // 2. Fix duplicate withdrawal transactions
    console.log('\n2. Fixing duplicate withdrawal transactions...');
    
    const duplicateWithdrawals = await prisma.transaction.findMany({
      where: {
        type: 'WITHDRAWAL',
        description: {
          contains: 'USDT withdrawal:'
        }
      },
      orderBy: [
        { userId: 'asc' },
        { reference: 'asc' },
        { createdAt: 'asc' }
      ]
    });

    console.log(`Found ${duplicateWithdrawals.length} withdrawal transactions`);

    // Group by user and reference (withdrawal request ID)
    const groupedWithdrawals = new Map<string, typeof duplicateWithdrawals>();
    
    duplicateWithdrawals.forEach(tx => {
      const key = `${tx.userId}-${tx.reference || 'no-ref'}`;
      if (!groupedWithdrawals.has(key)) {
        groupedWithdrawals.set(key, []);
      }
      groupedWithdrawals.get(key)!.push(tx);
    });

    let withdrawalDuplicatesRemoved = 0;

    for (const [key, transactions] of groupedWithdrawals) {
      if (transactions.length > 1) {
        console.log(`\n   Found ${transactions.length} duplicate withdrawals for: ${key}`);
        
        // Keep the first transaction (oldest), remove the rest
        const [keepTransaction, ...removeTransactions] = transactions;
        console.log(`   Keeping transaction: ${keepTransaction.id} (${keepTransaction.createdAt}) - Amount: ${keepTransaction.amount}`);

        for (const tx of removeTransactions) {
          console.log(`   Removing transaction: ${tx.id} (${tx.createdAt}) - Amount: ${tx.amount}`);
          
          // Remove the duplicate transaction
          await prisma.transaction.delete({
            where: { id: tx.id }
          });

          withdrawalDuplicatesRemoved++;
        }
      }
    }

    console.log(`\n✅ Withdrawal cleanup completed!`);
    console.log(`   Removed ${withdrawalDuplicatesRemoved} duplicate withdrawal transactions`);

    // 3. Update withdrawal transaction statuses based on withdrawal request status
    console.log('\n3. Updating withdrawal transaction statuses...');
    
    const withdrawalRequests = await prisma.withdrawalRequest.findMany({
      where: {
        status: { in: ['APPROVED', 'COMPLETED', 'REJECTED'] }
      }
    });

    let withdrawalStatusUpdated = 0;

    for (const request of withdrawalRequests) {
      const transaction = await prisma.transaction.findFirst({
        where: {
          reference: request.id,
          type: 'WITHDRAWAL',
        }
      });

      if (transaction) {
        let newStatus: 'COMPLETED' | 'CANCELLED' = 'COMPLETED';
        let newDescription = transaction.description;

        if (request.status === 'REJECTED') {
          newStatus = 'CANCELLED';
        } else if (request.status === 'COMPLETED' && request.txid) {
          // Add transaction hash to description if not already present
          if (!transaction.description.includes('TX:')) {
            newDescription = `${transaction.description} - TX: ${request.txid}`;
          }
        }

        if (transaction.status !== newStatus || transaction.description !== newDescription) {
          await prisma.transaction.update({
            where: { id: transaction.id },
            data: {
              status: newStatus,
              description: newDescription,
            }
          });

          console.log(`   Updated transaction ${transaction.id}: ${transaction.status} -> ${newStatus}`);
          withdrawalStatusUpdated++;
        }
      }
    }

    console.log(`\n✅ Withdrawal status update completed!`);
    console.log(`   Updated ${withdrawalStatusUpdated} withdrawal transaction statuses`);

    // 4. Final verification
    console.log('\n4. Final verification...');
    
    const remainingDepositDuplicates = await prisma.transaction.groupBy({
      by: ['userId', 'description'],
      where: {
        type: 'DEPOSIT',
        description: {
          contains: 'USDT TRC20 Deposit - TX:'
        }
      },
      _count: {
        id: true
      },
      having: {
        id: {
          _count: {
            gt: 1
          }
        }
      }
    });

    const remainingWithdrawalDuplicates = await prisma.transaction.groupBy({
      by: ['userId', 'reference'],
      where: {
        type: 'WITHDRAWAL',
        reference: { not: null }
      },
      _count: {
        id: true
      },
      having: {
        id: {
          _count: {
            gt: 1
          }
        }
      }
    });

    if (remainingDepositDuplicates.length === 0 && remainingWithdrawalDuplicates.length === 0) {
      console.log('✅ No remaining duplicates found!');
    } else {
      console.log(`⚠️  Still found ${remainingDepositDuplicates.length} deposit duplicate groups`);
      console.log(`⚠️  Still found ${remainingWithdrawalDuplicates.length} withdrawal duplicate groups`);
    }

    console.log('\n🎉 Transaction cleanup completed successfully!');

  } catch (error) {
    console.error('❌ Error during transaction cleanup:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the cleanup
fixDuplicateTransactions()
  .then(() => {
    console.log('✅ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
