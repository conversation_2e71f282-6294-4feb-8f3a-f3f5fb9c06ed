-- CreateEnum
CREATE TYPE "EarningType" AS ENUM ('MINING_EARNINGS', 'DIRECT_REFERRAL', 'BINARY_BONUS');

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "DepositStatus" ADD VALUE 'PENDING_VERIFICATION';
ALTER TYPE "DepositStatus" ADD VALUE 'WAITING_FOR_CONFIRMATIONS';

-- AlterTable
ALTER TABLE "mining_units" ADD COLUMN     "binaryEarnings" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "miningEarnings" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "referralEarnings" DOUBLE PRECISION NOT NULL DEFAULT 0;

-- CreateTable
CREATE TABLE "mining_unit_earnings_allocations" (
    "id" TEXT NOT NULL,
    "miningUnitId" TEXT NOT NULL,
    "transactionId" TEXT NOT NULL,
    "earningType" "EarningType" NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "allocatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "description" TEXT NOT NULL,

    CONSTRAINT "mining_unit_earnings_allocations_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "mining_unit_earnings_allocations" ADD CONSTRAINT "mining_unit_earnings_allocations_miningUnitId_fkey" FOREIGN KEY ("miningUnitId") REFERENCES "mining_units"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "mining_unit_earnings_allocations" ADD CONSTRAINT "mining_unit_earnings_allocations_transactionId_fkey" FOREIGN KEY ("transactionId") REFERENCES "transactions"("id") ON DELETE CASCADE ON UPDATE CASCADE;
