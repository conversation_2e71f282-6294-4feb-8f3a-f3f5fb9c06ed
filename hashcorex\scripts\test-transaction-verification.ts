import { PrismaClient } from '@prisma/client';
import { verifyUSDTTransaction, getTransactionById, getTransactionInfo, getCurrentBlock } from '@/lib/trongrid';

const prisma = new PrismaClient();

async function testTransactionVerification() {
  console.log('🧪 Testing transaction verification...\n');

  // Test transaction ID from the user
  const testTxId = '0439d3658e5a6fd7febc91f807b60cccdeda39e06ac26a13acb80f6d1616b145';
  const testAddress = 'TXLEkeZHx4dtGG6gxEjKhwWRJYYxrmV9t8'; // Actual recipient address from transaction

  try {
    console.log('📋 Test Details:');
    console.log(`   Transaction ID: ${testTxId}`);
    console.log(`   Test Address: ${testAddress}\n`);

    // Test 1: Get current block
    console.log('🔍 Test 1: Getting current block...');
    const currentBlock = await getCurrentBlock();
    if (currentBlock) {
      console.log(`✅ Current block: ${currentBlock.blockNumber}`);
    } else {
      console.log('❌ Failed to get current block');
    }
    console.log('');

    // Test 2: Get transaction by ID
    console.log('🔍 Test 2: Getting transaction by ID...');
    const transaction = await getTransactionById(testTxId);
    if (transaction) {
      console.log('✅ Transaction found:');
      console.log(`   TX ID: ${transaction.txID}`);
      console.log(`   Block Number: ${transaction.blockNumber}`);
      console.log(`   Block Timestamp: ${new Date(transaction.blockTimeStamp)}`);
    } else {
      console.log('❌ Transaction not found');
    }
    console.log('');

    // Test 3: Get transaction info
    console.log('🔍 Test 3: Getting transaction info...');
    const transactionInfo = await getTransactionInfo(testTxId);
    if (transactionInfo) {
      console.log('✅ Transaction info found:');
      console.log(`   ID: ${transactionInfo.id}`);
      console.log(`   Block Number: ${transactionInfo.blockNumber}`);
      console.log(`   Receipt Result: ${transactionInfo.receipt?.result}`);
    } else {
      console.log('❌ Transaction info not found');
    }
    console.log('');

    // Test 4: Full USDT verification
    console.log('🔍 Test 4: Full USDT transaction verification...');
    const verificationResult = await verifyUSDTTransaction(testTxId, testAddress, 1);
    
    console.log('📊 Verification Result:');
    console.log(`   Valid: ${verificationResult.isValid}`);
    console.log(`   Amount: ${verificationResult.amount} USDT`);
    console.log(`   From: ${verificationResult.fromAddress}`);
    console.log(`   To: ${verificationResult.toAddress}`);
    console.log(`   Block: ${verificationResult.blockNumber}`);
    console.log(`   Confirmations: ${verificationResult.confirmations}`);
    console.log(`   Timestamp: ${new Date(verificationResult.blockTimestamp)}`);
    
    if (!verificationResult.isValid) {
      console.log(`   Error: ${verificationResult.error}`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testTransactionVerification().catch(console.error);
