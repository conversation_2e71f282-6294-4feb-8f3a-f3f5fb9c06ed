{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/WalletDashboard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Button, Input, Card, CardHeader, CardTitle, CardContent, Modal, useConfirmDialog, useMessageBox } from '@/components/ui';\nimport { Grid } from '@/components/layout';\nimport { Wallet, ArrowUpRight, ArrowDownLeft, Clock, CheckCircle, XCircle, Copy, DollarSign, Search, Filter, RefreshCw, Shield, AlertTriangle } from 'lucide-react';\nimport { formatCurrency, formatDateTime, copyToClipboard } from '@/lib/utils';\nimport { DepositPage } from '@/components/wallet/DepositPage';\n\ninterface WalletData {\n  balance: number;\n  pendingEarnings: number;\n  recentTransactions: Array<{\n    id: string;\n    type: string;\n    amount: number;\n    description: string;\n    status: string;\n    createdAt: string;\n  }>;\n}\n\ninterface TransactionData {\n  id: string;\n  type: string;\n  category: string;\n  amount: number;\n  description: string;\n  status: string;\n  reference?: string;\n  createdAt: string;\n  // Additional details for modal\n  txid?: string | null;\n  usdtAddress?: string | null;\n  rejectionReason?: string | null;\n  processedAt?: string | null;\n  confirmations?: number;\n  blockNumber?: string;\n  senderAddress?: string;\n}\n\n\n\ninterface WithdrawalSettings {\n  minWithdrawalAmount: number;\n  fixedFee: number;\n  percentageFee: number;\n  processingDays: number;\n}\n\nexport const WalletDashboard: React.FC = () => {\n  const [activeTab, setActiveTab] = useState('overview');\n  const [walletData, setWalletData] = useState<WalletData | null>(null);\n  const [withdrawalSettings, setWithdrawalSettings] = useState<WithdrawalSettings | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [showWithdrawModal, setShowWithdrawModal] = useState(false);\n  const [withdrawalForm, setWithdrawalForm] = useState({\n    amount: '',\n    usdtAddress: '',\n  });\n  const [userWithdrawalAddress, setUserWithdrawalAddress] = useState('');\n  const [withdrawalLoading, setWithdrawalLoading] = useState(false);\n  const [withdrawalError, setWithdrawalError] = useState('');\n\n  // Dialog hooks\n  const { showConfirm, hideConfirm, ConfirmDialogComponent } = useConfirmDialog();\n  const { showMessage, hideMessage, MessageBoxComponent } = useMessageBox();\n\n  // Transaction filtering states\n  const [allTransactions, setAllTransactions] = useState<TransactionData[]>([]);\n  const [transactionLoading, setTransactionLoading] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('ALL');\n  const [filterStatus, setFilterStatus] = useState('ALL');\n  const [showFilters, setShowFilters] = useState(false);\n  const [transactionTypes, setTransactionTypes] = useState<string[]>([]);\n  const [statusOptions, setStatusOptions] = useState<string[]>([]);\n\n  // Transaction detail modal states\n  const [selectedTransaction, setSelectedTransaction] = useState<TransactionData | null>(null);\n  const [showTransactionModal, setShowTransactionModal] = useState(false);\n\n  useEffect(() => {\n    fetchWalletData();\n    fetchWithdrawalSettings();\n    fetchAllTransactions();\n    fetchUserWithdrawalAddress();\n\n    // Set up automatic refresh every 30 seconds (background only)\n    const refreshInterval = setInterval(() => {\n      // Only refresh data in background without affecting loading states\n      fetchWalletDataSilent();\n      fetchAllTransactionsSilent();\n    }, 30000);\n\n    return () => {\n      clearInterval(refreshInterval);\n    };\n  }, []);\n\n  // Fetch transactions when filters change\n  useEffect(() => {\n    fetchAllTransactions();\n  }, [searchTerm, filterType, filterStatus]);\n\n  const fetchWalletData = async () => {\n    try {\n      const response = await fetch('/api/wallet/balance', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setWalletData(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch wallet data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Silent fetch for background updates (no loading state changes)\n  const fetchWalletDataSilent = async () => {\n    try {\n      const response = await fetch('/api/wallet/balance', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setWalletData(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch wallet data silently:', error);\n    }\n  };\n\n  const fetchAllTransactions = async () => {\n    try {\n      setTransactionLoading(true);\n      const params = new URLSearchParams();\n      params.append('limit', '50');\n\n      if (searchTerm) params.append('search', searchTerm);\n      if (filterType !== 'ALL') params.append('type', filterType);\n      if (filterStatus !== 'ALL') params.append('status', filterStatus);\n\n      const response = await fetch(`/api/wallet/transactions?${params}`, {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setAllTransactions(data.data.transactions);\n          setTransactionTypes(data.data.filters.transactionTypes);\n          setStatusOptions(data.data.filters.statusOptions);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch transactions:', error);\n    } finally {\n      setTransactionLoading(false);\n    }\n  };\n\n  // Silent fetch for background updates (no loading state changes)\n  const fetchAllTransactionsSilent = async () => {\n    try {\n      const params = new URLSearchParams();\n      params.append('limit', '50');\n\n      if (searchTerm) params.append('search', searchTerm);\n      if (filterType !== 'ALL') params.append('type', filterType);\n      if (filterStatus !== 'ALL') params.append('status', filterStatus);\n\n      const response = await fetch(`/api/wallet/transactions?${params}`, {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setAllTransactions(data.data.transactions);\n          setTransactionTypes(data.data.filters.transactionTypes);\n          setStatusOptions(data.data.filters.statusOptions);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch transactions silently:', error);\n    }\n  };\n\n  const handleTransactionClick = (transaction: TransactionData) => {\n    setSelectedTransaction(transaction);\n    setShowTransactionModal(true);\n  };\n\n  const fetchWithdrawalSettings = async () => {\n    try {\n      const response = await fetch('/api/wallet/withdrawal-settings', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setWithdrawalSettings(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch withdrawal settings:', error);\n    }\n  };\n\n  const fetchUserWithdrawalAddress = async () => {\n    try {\n      const response = await fetch('/api/user/withdrawal-address', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success && data.data.withdrawalAddress) {\n          setUserWithdrawalAddress(data.data.withdrawalAddress);\n          // Auto-fill the withdrawal form if address is not already set\n          if (!withdrawalForm.usdtAddress) {\n            setWithdrawalForm(prev => ({\n              ...prev,\n              usdtAddress: data.data.withdrawalAddress\n            }));\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch user withdrawal address:', error);\n    }\n  };\n\n  const handleWithdrawalSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setWithdrawalError('');\n\n    try {\n      const amount = parseFloat(withdrawalForm.amount);\n\n      if (!amount || amount <= 0) {\n        throw new Error('Please enter a valid amount');\n      }\n\n      if (!withdrawalForm.usdtAddress) {\n        throw new Error('Please enter a USDT address');\n      }\n\n      // Calculate fees for confirmation\n      const fixedFee = withdrawalSettings?.fixedFee || 3;\n      const percentageFee = (amount * (withdrawalSettings?.percentageFee || 1)) / 100;\n      const totalFees = fixedFee + percentageFee;\n      const youReceive = amount - totalFees;\n\n      // Show confirmation dialog\n      showConfirm({\n        title: 'Confirm Withdrawal',\n        message: (\n          <div className=\"space-y-4\">\n            <div className=\"bg-gray-50 p-4 rounded-lg space-y-2\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Withdrawal Amount:</span>\n                <span className=\"font-semibold\">${amount.toFixed(2)} USDT</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Fixed Fee:</span>\n                <span className=\"text-red-600\">-${fixedFee.toFixed(2)} USDT</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Percentage Fee (1%):</span>\n                <span className=\"text-red-600\">-${percentageFee.toFixed(2)} USDT</span>\n              </div>\n              <div className=\"border-t pt-2 flex justify-between font-semibold\">\n                <span className=\"text-green-600\">You Receive:</span>\n                <span className=\"text-green-600\">${youReceive.toFixed(2)} USDT</span>\n              </div>\n            </div>\n            <div className=\"bg-blue-50 p-3 rounded-lg\">\n              <p className=\"text-sm text-blue-800\">\n                <strong>USDT Address:</strong><br />\n                {withdrawalForm.usdtAddress}\n              </p>\n            </div>\n            <div className=\"bg-yellow-50 p-3 rounded-lg\">\n              <p className=\"text-sm text-yellow-800\">\n                ⚠️ Please double-check your address. Transactions cannot be reversed.\n              </p>\n            </div>\n          </div>\n        ),\n        confirmText: 'Confirm Withdrawal',\n        cancelText: 'Cancel',\n        variant: 'warning',\n        onConfirm: () => processWithdrawal(amount),\n      });\n\n    } catch (err: any) {\n      setWithdrawalError(err.message || 'Invalid withdrawal details');\n    }\n  };\n\n  const processWithdrawal = async (amount: number) => {\n    setWithdrawalLoading(true);\n\n    try {\n      const response = await fetch('/api/wallet/withdraw', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({\n          amount,\n          usdtAddress: withdrawalForm.usdtAddress,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!data.success) {\n        throw new Error(data.error || 'Withdrawal failed');\n      }\n\n      // Show success message\n      showMessage({\n        title: 'Withdrawal Submitted',\n        message: 'Your withdrawal request has been submitted successfully. It will be processed within 3 business days.',\n        variant: 'success',\n        buttonText: 'OK',\n      });\n\n      // Reset form and close modal\n      setWithdrawalForm({ amount: '', usdtAddress: '' });\n      setActiveTab('overview');\n\n      // Refresh data\n      fetchWalletData();\n      fetchAllTransactions();\n\n    } catch (err: any) {\n      showMessage({\n        title: 'Withdrawal Failed',\n        message: err.message || 'Failed to process withdrawal. Please try again.',\n        variant: 'error',\n        buttonText: 'OK',\n      });\n    } finally {\n      setWithdrawalLoading(false);\n    }\n  };\n\n  const getTransactionIcon = (type: string) => {\n    switch (type) {\n      case 'WITHDRAWAL':\n        return <ArrowUpRight className=\"h-4 w-4 text-red-500\" />;\n      case 'DEPOSIT':\n      case 'MINING_EARNINGS':\n      case 'DIRECT_REFERRAL':\n      case 'BINARY_BONUS':\n        return <ArrowDownLeft className=\"h-4 w-4 text-eco-500\" />;\n      default:\n        return <Wallet className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const getTransactionColor = (type: string) => {\n    switch (type) {\n      case 'WITHDRAWAL':\n      case 'PURCHASE':\n        return 'text-red-600';\n      case 'DEPOSIT':\n      case 'MINING_EARNINGS':\n      case 'DIRECT_REFERRAL':\n      case 'BINARY_BONUS':\n        return 'text-eco-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'COMPLETED':\n      case 'APPROVED':\n        return <CheckCircle className=\"h-4 w-4 text-eco-500\" />;\n      case 'PENDING':\n        return <Clock className=\"h-4 w-4 text-solar-500\" />;\n      case 'FAILED':\n      case 'REJECTED':\n        return <XCircle className=\"h-4 w-4 text-red-500\" />;\n      default:\n        return <Clock className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n\n\n  const calculateWithdrawalFees = (amount: number) => {\n    if (!withdrawalSettings || !amount) {\n      return { fixedFee: 0, percentageFee: 0, totalFees: 0, totalDeduction: 0, netAmount: 0 };\n    }\n\n    const fixedFee = withdrawalSettings.fixedFee;\n    const percentageFee = (amount * withdrawalSettings.percentageFee) / 100;\n    const totalFees = fixedFee + percentageFee;\n    const totalDeduction = amount + totalFees;\n    const netAmount = amount;\n\n    return { fixedFee, percentageFee, totalFees, totalDeduction, netAmount };\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-32 bg-gray-200 rounded-xl\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!walletData) {\n    return (\n      <Card>\n        <CardContent className=\"text-center py-8\">\n          <p className=\"text-gray-500\">Failed to load wallet data</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const tabs = [\n    { id: 'overview', label: 'Overview', icon: Wallet },\n    { id: 'deposit', label: 'Deposit', icon: ArrowDownLeft },\n    { id: 'withdraw', label: 'Withdraw', icon: ArrowUpRight },\n  ];\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'deposit':\n        return <DepositPage />;\n      case 'withdraw':\n        return renderWithdrawContent();\n      default:\n        return renderOverviewContent();\n    }\n  };\n\n  const renderOverviewContent = () => (\n    <div className=\"space-y-8\">\n      {/* Wallet Overview */}\n      <div>\n        <Grid cols={{ default: 1, lg: 2 }} gap={8}>\n          <Card>\n            <CardContent className=\"p-8\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Available Balance</p>\n                  <p className=\"text-4xl font-bold text-dark-900\">\n                    {formatCurrency(walletData.balance)}\n                  </p>\n                </div>\n                <div className=\"h-16 w-16 bg-eco-100 rounded-xl flex items-center justify-center\">\n                  <Wallet className=\"h-8 w-8 text-eco-600\" />\n                </div>\n              </div>\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3\">\n                <Button\n                  onClick={() => setActiveTab('deposit')}\n                  variant=\"outline\"\n                  className=\"h-12 text-base font-semibold rounded-xl\"\n                >\n                  <ArrowDownLeft className=\"h-5 w-5 mr-2\" />\n                  Deposit\n                </Button>\n                <Button\n                  onClick={() => setActiveTab('withdraw')}\n                  className=\"h-12 text-base font-semibold rounded-xl\"\n                  disabled={walletData.balance < 10}\n                >\n                  <ArrowUpRight className=\"h-5 w-5 mr-2\" />\n                  Withdraw\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-8\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Pending Earnings</p>\n                  <p className=\"text-4xl font-bold text-solar-600\">\n                    {formatCurrency(walletData.pendingEarnings)}\n                  </p>\n                </div>\n                <div className=\"h-16 w-16 bg-solar-100 rounded-xl flex items-center justify-center\">\n                  <Clock className=\"h-8 w-8 text-solar-600\" />\n                </div>\n              </div>\n              <p className=\"text-sm text-gray-600 font-medium\">\n                Will be transferred on Saturday at 15:00 UTC\n              </p>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Enhanced Transactions */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex flex-col sm:flex-row sm:items-center justify-between gap-4\">\n            <CardTitle>Transaction History</CardTitle>\n            <div className=\"flex flex-col sm:flex-row items-stretch sm:items-center gap-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setShowFilters(!showFilters)}\n                className=\"flex items-center justify-center space-x-2 w-full sm:w-auto\"\n              >\n                <Filter className=\"h-4 w-4\" />\n                <span>Filters</span>\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={fetchAllTransactions}\n                disabled={transactionLoading}\n                className=\"flex items-center justify-center space-x-2 w-full sm:w-auto\"\n              >\n                <RefreshCw className={`h-4 w-4 ${transactionLoading ? 'animate-spin' : ''}`} />\n                <span>Refresh</span>\n              </Button>\n            </div>\n          </div>\n        </CardHeader>\n        <CardContent>\n          {/* Search and Filters */}\n          <div className=\"space-y-4 mb-6\">\n            {/* Search Bar */}\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n              <Input\n                placeholder=\"Search transactions by description, type, or reference...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10\"\n              />\n            </div>\n\n            {/* Filter Options */}\n            {showFilters && (\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Transaction Type\n                  </label>\n                  <select\n                    value={filterType}\n                    onChange={(e) => setFilterType(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-eco-500 text-sm\"\n                  >\n                    {transactionTypes.map(type => (\n                      <option key={type} value={type}>\n                        {type === 'ALL' ? 'All Types' : type.replace('_', ' ')}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Status\n                  </label>\n                  <select\n                    value={filterStatus}\n                    onChange={(e) => setFilterStatus(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-eco-500 text-sm\"\n                  >\n                    {statusOptions.map(status => (\n                      <option key={status} value={status}>\n                        {status === 'ALL' ? 'All Status' : status.replace('_', ' ')}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Transaction List */}\n          {transactionLoading ? (\n            <div className=\"text-center py-8\">\n              <RefreshCw className=\"h-8 w-8 animate-spin mx-auto text-gray-400 mb-2\" />\n              <p className=\"text-gray-500\">Loading transactions...</p>\n            </div>\n          ) : allTransactions.length > 0 ? (\n            <div className=\"space-y-3\">\n              {allTransactions.map((transaction) => (\n                <div\n                  key={transaction.id}\n                  className=\"flex flex-col sm:flex-row sm:items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer gap-3\"\n                  onClick={() => handleTransactionClick(transaction)}\n                >\n                  <div className=\"flex items-start sm:items-center space-x-3 flex-1 min-w-0\">\n                    <div className=\"flex-shrink-0 mt-1 sm:mt-0\">\n                      {getTransactionIcon(transaction.type)}\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <p className=\"font-medium text-dark-900 truncate\">{transaction.description}</p>\n                      <div className=\"flex flex-col sm:flex-row sm:items-center sm:space-x-4 text-sm text-gray-500 mt-1 gap-1 sm:gap-0\">\n                        <span className=\"text-xs sm:text-sm\">{formatDateTime(transaction.createdAt)}</span>\n                        <span className=\"px-2 py-1 bg-gray-200 rounded text-xs w-fit\">\n                          {transaction.type.replace('_', ' ')}\n                        </span>\n                        {transaction.reference && (\n                          <span\n                            className=\"px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs cursor-pointer hover:bg-blue-200 w-fit\"\n                            onClick={(e) => {\n                              e.stopPropagation();\n                              copyToClipboard(transaction.reference!);\n                            }}\n                            title=\"Click to copy reference\"\n                          >\n                            Ref: {transaction.reference.substring(0, 8)}...\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center justify-between sm:justify-end sm:space-x-3 gap-2 flex-shrink-0\">\n                    <span className={`font-semibold text-lg sm:text-base ${getTransactionColor(transaction.type)}`}>\n                      {transaction.type === 'WITHDRAWAL' || transaction.type === 'PURCHASE' ? '-' : '+'}\n                      {formatCurrency(transaction.amount)}\n                    </span>\n                    <div className=\"flex-shrink-0\">\n                      {getStatusIcon(transaction.status)}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <p className=\"text-gray-500\">No transactions found</p>\n              {(searchTerm || filterType !== 'ALL' || filterStatus !== 'ALL') && (\n                <p className=\"text-sm text-gray-400 mt-2\">\n                  Try adjusting your search or filter criteria\n                </p>\n              )}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n\n  const renderWithdrawContent = () => {\n    const amount = parseFloat(withdrawalForm.amount) || 0;\n    const feeCalculation = calculateWithdrawalFees(amount);\n\n    return (\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900\">Withdraw USDT</h2>\n          <p className=\"text-gray-600 mt-1\">Withdraw funds from your wallet to your USDT TRC20 address</p>\n        </div>\n\n        {/* Balance Overview */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <Card className=\"bg-white border-gray-200 shadow-sm\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-gray-600 text-sm\">Available Balance</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{formatCurrency(walletData?.balance || 0)} USDT</p>\n                </div>\n                <Wallet className=\"w-8 h-8 text-green-600\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-white border-gray-200 shadow-sm\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-gray-600 text-sm\">Minimum Withdrawal</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{formatCurrency(withdrawalSettings?.minWithdrawalAmount || 10)} USDT</p>\n                </div>\n                <ArrowUpRight className=\"w-8 h-8 text-blue-600\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-white border-gray-200 shadow-sm\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-gray-600 text-sm\">Network</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">USDT (TRC20)</p>\n                </div>\n                <Shield className=\"w-8 h-8 text-purple-600\" />\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Withdrawal Form */}\n        <Card className=\"bg-white border-gray-200 shadow-sm\">\n          <CardHeader>\n            <CardTitle className=\"text-gray-900\">Withdrawal Details</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <form onSubmit={handleWithdrawalSubmit} className=\"space-y-6\">\n              {withdrawalError && (\n                <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm\">\n                  {withdrawalError}\n                </div>\n              )}\n\n              <div className=\"space-y-6\">\n                {/* Input Fields */}\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Withdrawal Amount (USDT)\n                    </label>\n                    <Input\n                      type=\"number\"\n                      step=\"0.01\"\n                      min={withdrawalSettings?.minWithdrawalAmount || 10}\n                      max={walletData?.balance || 0}\n                      value={withdrawalForm.amount}\n                      onChange={(e) => setWithdrawalForm(prev => ({ ...prev, amount: e.target.value }))}\n                      placeholder=\"Enter amount to withdraw\"\n                      className=\"bg-white border-gray-300 text-gray-900\"\n                      required\n                    />\n                    <p className=\"text-xs text-gray-500 mt-1\">\n                      Min: {formatCurrency(withdrawalSettings?.minWithdrawalAmount || 10)} USDT\n                    </p>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      USDT TRC20 Address\n                    </label>\n                    <Input\n                      type=\"text\"\n                      value={withdrawalForm.usdtAddress}\n                      onChange={(e) => setWithdrawalForm(prev => ({ ...prev, usdtAddress: e.target.value }))}\n                      placeholder=\"Enter your USDT TRC20 address\"\n                      className=\"bg-white border-gray-300 text-gray-900\"\n                      required\n                    />\n                    <p className=\"text-xs text-gray-500 mt-1\">\n                      {userWithdrawalAddress && withdrawalForm.usdtAddress === userWithdrawalAddress\n                        ? 'Auto-filled from your saved withdrawal address'\n                        : 'Only TRC20 addresses are supported'\n                      }\n                    </p>\n                  </div>\n                </div>\n\n                {/* Fee Calculation Display */}\n                {amount > 0 && (\n                  <Card className=\"bg-gray-50 border-gray-200\">\n                    <CardHeader>\n                      <CardTitle className=\"text-lg text-gray-900\">Transaction Summary</CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"space-y-3\">\n                        <div className=\"flex justify-between items-center\">\n                          <span className=\"text-gray-600\">Withdrawal Amount:</span>\n                          <span className=\"font-semibold text-gray-900\">{formatCurrency(amount)} USDT</span>\n                        </div>\n                        <div className=\"flex justify-between items-center\">\n                          <span className=\"text-gray-600\">Fixed Fee:</span>\n                          <span className=\"font-semibold text-red-600\">-{formatCurrency(feeCalculation.fixedFee)} USDT</span>\n                        </div>\n                        <div className=\"flex justify-between items-center\">\n                          <span className=\"text-gray-600\">Percentage Fee ({withdrawalSettings?.percentageFee || 0}%):</span>\n                          <span className=\"font-semibold text-red-600\">-{formatCurrency(feeCalculation.percentageFee)} USDT</span>\n                        </div>\n                        <div className=\"border-t border-gray-300 pt-3\">\n                          <div className=\"flex justify-between items-center\">\n                            <span className=\"text-gray-900 font-medium\">Total Fees:</span>\n                            <span className=\"font-bold text-red-600\">{formatCurrency(feeCalculation.totalFees)} USDT</span>\n                          </div>\n                          <div className=\"flex justify-between items-center mt-2\">\n                            <span className=\"text-gray-900 font-medium\">Total Deduction:</span>\n                            <span className=\"font-bold text-red-600\">{formatCurrency(feeCalculation.totalDeduction)} USDT</span>\n                          </div>\n                          <div className=\"flex justify-between items-center mt-3 p-3 bg-green-50 rounded-lg\">\n                            <span className=\"text-green-800 font-bold text-lg\">You Receive:</span>\n                            <span className=\"font-bold text-green-600 text-xl\">{formatCurrency(feeCalculation.netAmount)} USDT</span>\n                          </div>\n                        </div>\n                      </div>\n                    </CardContent>\n                  </Card>\n                )}\n              </div>\n\n              {/* Important Notice */}\n              <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                <div className=\"flex items-start space-x-3\">\n                  <AlertTriangle className=\"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0\" />\n                  <div>\n                    <h4 className=\"font-medium text-yellow-800 mb-2\">Important Information</h4>\n                    <ul className=\"text-sm text-yellow-700 space-y-1\">\n                      <li>• Only USDT TRC20 addresses are supported</li>\n                      <li>• Withdrawals require KYC verification</li>\n                      <li>• Processing time: {withdrawalSettings?.processingDays || 3} business days</li>\n                      <li>• Double-check your address - transactions cannot be reversed</li>\n                      <li>• Fees are deducted from your balance in addition to the withdrawal amount</li>\n                    </ul>\n                  </div>\n                </div>\n              </div>\n\n              {/* Action Buttons */}\n              <div className=\"flex flex-col sm:flex-row gap-3\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => setActiveTab('overview')}\n                  className=\"flex-1 border-gray-300 text-gray-700 hover:bg-gray-50\"\n                >\n                  Back to Overview\n                </Button>\n                <Button\n                  type=\"submit\"\n                  loading={withdrawalLoading}\n                  className=\"flex-1 bg-yellow-500 hover:bg-yellow-600 text-white\"\n                  disabled={\n                    !walletData ||\n                    !withdrawalSettings ||\n                    amount < (withdrawalSettings?.minWithdrawalAmount || 10) ||\n                    feeCalculation.totalDeduction > walletData.balance\n                  }\n                >\n                  {feeCalculation.totalDeduction > (walletData?.balance || 0)\n                    ? 'Insufficient Balance'\n                    : 'Submit Withdrawal'\n                  }\n                </Button>\n              </div>\n            </form>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon;\n            const isActive = activeTab === tab.id;\n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`\n                  flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors\n                  ${isActive\n                    ? 'border-solar-500 text-solar-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }\n                `}\n              >\n                <Icon className=\"h-4 w-4\" />\n                <span>{tab.label}</span>\n              </button>\n            );\n          })}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      {renderTabContent()}\n\n      {/* Transaction Detail Modal */}\n      {showTransactionModal && selectedTransaction && (\n        <Modal\n          isOpen={showTransactionModal}\n          onClose={() => setShowTransactionModal(false)}\n          title=\"Transaction Details\"\n          size=\"lg\"\n        >\n          <div className=\"space-y-6\">\n            {/* Transaction Header */}\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n              <div className=\"flex items-center space-x-3\">\n                {getTransactionIcon(selectedTransaction.type)}\n                <div>\n                  <h3 className=\"font-semibold text-lg text-dark-900\">\n                    {selectedTransaction.description}\n                  </h3>\n                  <p className=\"text-sm text-gray-600\">\n                    {selectedTransaction.type.replace('_', ' ')} • {formatDateTime(selectedTransaction.createdAt)}\n                  </p>\n                </div>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"flex items-center space-x-2 mb-1\">\n                  {getStatusIcon(selectedTransaction.status)}\n                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${\n                    selectedTransaction.status === 'COMPLETED' || selectedTransaction.status === 'CONFIRMED' ? 'bg-eco-100 text-eco-700' :\n                    selectedTransaction.status === 'PENDING' ? 'bg-solar-100 text-solar-700' :\n                    'bg-red-100 text-red-700'\n                  }`}>\n                    {selectedTransaction.status}\n                  </span>\n                </div>\n                <p className={`text-xl font-bold ${getTransactionColor(selectedTransaction.type)}`}>\n                  {selectedTransaction.type === 'WITHDRAWAL' || selectedTransaction.type === 'PURCHASE' ? '-' : '+'}\n                  {formatCurrency(selectedTransaction.amount)}\n                </p>\n              </div>\n            </div>\n\n            {/* Transaction Details */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-3\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">Transaction ID</label>\n                  <div className=\"flex items-start space-x-2\">\n                    <p className=\"text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all flex-1 min-w-0\">\n                      {selectedTransaction.id}\n                    </p>\n                    <button\n                      onClick={() => copyToClipboard(selectedTransaction.id)}\n                      className=\"text-gray-400 hover:text-gray-600 flex-shrink-0 mt-1\"\n                    >\n                      <Copy className=\"h-4 w-4\" />\n                    </button>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">Type</label>\n                  <p className=\"text-sm text-gray-900\">{selectedTransaction.type.replace('_', ' ')}</p>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">Status</label>\n                  <div className=\"flex items-center space-x-2\">\n                    {getStatusIcon(selectedTransaction.status)}\n                    <span className=\"text-sm text-gray-900\">{selectedTransaction.status}</span>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">Date & Time</label>\n                  <p className=\"text-sm text-gray-900\">{formatDateTime(selectedTransaction.createdAt)}</p>\n                </div>\n              </div>\n\n              <div className=\"space-y-3\">\n                {selectedTransaction.reference && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Reference</label>\n                    <div className=\"flex items-start space-x-2\">\n                      <p className=\"text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all flex-1 min-w-0\">\n                        {selectedTransaction.reference}\n                      </p>\n                      <button\n                        onClick={() => copyToClipboard(selectedTransaction.reference!)}\n                        className=\"text-gray-400 hover:text-gray-600 flex-shrink-0 mt-1\"\n                      >\n                        <Copy className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </div>\n                )}\n\n                {selectedTransaction.txid && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Transaction Hash</label>\n                    <div className=\"flex items-start space-x-2\">\n                      <p className=\"text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all flex-1 min-w-0\">\n                        {selectedTransaction.txid}\n                      </p>\n                      <button\n                        onClick={() => copyToClipboard(selectedTransaction.txid!)}\n                        className=\"text-gray-400 hover:text-gray-600 flex-shrink-0 mt-1\"\n                      >\n                        <Copy className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </div>\n                )}\n\n                {selectedTransaction.usdtAddress && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      {selectedTransaction.type === 'DEPOSIT' ? 'Deposit Address' : 'Withdrawal Address'}\n                    </label>\n                    <div className=\"flex items-center space-x-2\">\n                      <p className=\"text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all\">\n                        {selectedTransaction.usdtAddress}\n                      </p>\n                      <button\n                        onClick={() => copyToClipboard(selectedTransaction.usdtAddress!)}\n                        className=\"text-gray-400 hover:text-gray-600 flex-shrink-0\"\n                      >\n                        <Copy className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </div>\n                )}\n\n                {selectedTransaction.confirmations !== undefined && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Confirmations</label>\n                    <p className=\"text-sm text-gray-900\">{selectedTransaction.confirmations}</p>\n                  </div>\n                )}\n\n                {selectedTransaction.processedAt && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Processed At</label>\n                    <p className=\"text-sm text-gray-900\">{formatDateTime(selectedTransaction.processedAt)}</p>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Additional Information */}\n            {selectedTransaction.rejectionReason && (\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n                <h4 className=\"font-medium text-red-800 mb-2\">Rejection Reason</h4>\n                <p className=\"text-sm text-red-700\">{selectedTransaction.rejectionReason}</p>\n              </div>\n            )}\n\n            {selectedTransaction.senderAddress && (\n              <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                <h4 className=\"font-medium text-blue-800 mb-2\">Sender Information</h4>\n                <div className=\"flex items-center space-x-2\">\n                  <p className=\"text-sm text-blue-700 font-mono\">{selectedTransaction.senderAddress}</p>\n                  <button\n                    onClick={() => copyToClipboard(selectedTransaction.senderAddress!)}\n                    className=\"text-blue-400 hover:text-blue-600\"\n                  >\n                    <Copy className=\"h-4 w-4\" />\n                  </button>\n                </div>\n              </div>\n            )}\n\n            {/* Action Buttons */}\n            <div className=\"flex justify-end space-x-3 pt-4 border-t\">\n              <Button\n                variant=\"outline\"\n                onClick={() => setShowTransactionModal(false)}\n              >\n                Close\n              </Button>\n              {selectedTransaction.txid && (\n                <Button\n                  onClick={() => {\n                    const explorerUrl = `https://tronscan.org/#/transaction/${selectedTransaction.txid}`;\n                    window.open(explorerUrl, '_blank');\n                  }}\n                  className=\"flex items-center space-x-2\"\n                >\n                  <span>View on Explorer</span>\n                  <ArrowUpRight className=\"h-4 w-4\" />\n                </Button>\n              )}\n            </div>\n          </div>\n        </Modal>\n      )}\n\n      {/* Dialog Components */}\n      <ConfirmDialogComponent />\n      <MessageBoxComponent />\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AAkDO,MAAM,kBAA4B;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IACxF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,QAAQ;QACR,aAAa;IACf;IACA,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,eAAe;IACf,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAC5E,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;IAEtE,+BAA+B;IAC/B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC5E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,kCAAkC;IAClC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACvF,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;QACA;QACA;QAEA,8DAA8D;QAC9D,MAAM,kBAAkB,YAAY;YAClC,mEAAmE;YACnE;YACA;QACF,GAAG;QAEH,OAAO;YACL,cAAc;QAChB;IACF,GAAG,EAAE;IAEL,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAY;QAAY;KAAa;IAEzC,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,cAAc,KAAK,IAAI;gBACzB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,iEAAiE;IACjE,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,cAAc,KAAK,IAAI;gBACzB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;QACzD;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,sBAAsB;YACtB,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,CAAC,SAAS;YAEvB,IAAI,YAAY,OAAO,MAAM,CAAC,UAAU;YACxC,IAAI,eAAe,OAAO,OAAO,MAAM,CAAC,QAAQ;YAChD,IAAI,iBAAiB,OAAO,OAAO,MAAM,CAAC,UAAU;YAEpD,MAAM,WAAW,MAAM,MAAM,CAAC,yBAAyB,EAAE,QAAQ,EAAE;gBACjE,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,mBAAmB,KAAK,IAAI,CAAC,YAAY;oBACzC,oBAAoB,KAAK,IAAI,CAAC,OAAO,CAAC,gBAAgB;oBACtD,iBAAiB,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa;gBAClD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,iEAAiE;IACjE,MAAM,6BAA6B;QACjC,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,CAAC,SAAS;YAEvB,IAAI,YAAY,OAAO,MAAM,CAAC,UAAU;YACxC,IAAI,eAAe,OAAO,OAAO,MAAM,CAAC,QAAQ;YAChD,IAAI,iBAAiB,OAAO,OAAO,MAAM,CAAC,UAAU;YAEpD,MAAM,WAAW,MAAM,MAAM,CAAC,yBAAyB,EAAE,QAAQ,EAAE;gBACjE,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,mBAAmB,KAAK,IAAI,CAAC,YAAY;oBACzC,oBAAoB,KAAK,IAAI,CAAC,OAAO,CAAC,gBAAgB;oBACtD,iBAAiB,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa;gBAClD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,uBAAuB;QACvB,wBAAwB;IAC1B;IAEA,MAAM,0BAA0B;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mCAAmC;gBAC9D,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,sBAAsB,KAAK,IAAI;gBACjC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD;IACF;IAEA,MAAM,6BAA6B;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gCAAgC;gBAC3D,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,CAAC,iBAAiB,EAAE;oBAC/C,yBAAyB,KAAK,IAAI,CAAC,iBAAiB;oBACpD,8DAA8D;oBAC9D,IAAI,CAAC,eAAe,WAAW,EAAE;wBAC/B,kBAAkB,CAAA,OAAQ,CAAC;gCACzB,GAAG,IAAI;gCACP,aAAa,KAAK,IAAI,CAAC,iBAAiB;4BAC1C,CAAC;oBACH;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;QAC5D;IACF;IAEA,MAAM,yBAAyB,OAAO;QACpC,EAAE,cAAc;QAChB,mBAAmB;QAEnB,IAAI;YACF,MAAM,SAAS,WAAW,eAAe,MAAM;YAE/C,IAAI,CAAC,UAAU,UAAU,GAAG;gBAC1B,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,eAAe,WAAW,EAAE;gBAC/B,MAAM,IAAI,MAAM;YAClB;YAEA,kCAAkC;YAClC,MAAM,WAAW,oBAAoB,YAAY;YACjD,MAAM,gBAAgB,AAAC,SAAS,CAAC,oBAAoB,iBAAiB,CAAC,IAAK;YAC5E,MAAM,YAAY,WAAW;YAC7B,MAAM,aAAa,SAAS;YAE5B,2BAA2B;YAC3B,YAAY;gBACV,OAAO;gBACP,uBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAK,WAAU;;gDAAgB;gDAAE,OAAO,OAAO,CAAC;gDAAG;;;;;;;;;;;;;8CAEtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAK,WAAU;;gDAAe;gDAAG,SAAS,OAAO,CAAC;gDAAG;;;;;;;;;;;;;8CAExD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAK,WAAU;;gDAAe;gDAAG,cAAc,OAAO,CAAC;gDAAG;;;;;;;;;;;;;8CAE7D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,8OAAC;4CAAK,WAAU;;gDAAiB;gDAAE,WAAW,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;;sCAG7D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;kDAAO;;;;;;kDAAsB,8OAAC;;;;;oCAC9B,eAAe,WAAW;;;;;;;;;;;;sCAG/B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA0B;;;;;;;;;;;;;;;;;gBAM7C,aAAa;gBACb,YAAY;gBACZ,SAAS;gBACT,WAAW,IAAM,kBAAkB;YACrC;QAEF,EAAE,OAAO,KAAU;YACjB,mBAAmB,IAAI,OAAO,IAAI;QACpC;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,qBAAqB;QAErB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,aAAa,eAAe,WAAW;gBACzC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,uBAAuB;YACvB,YAAY;gBACV,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,YAAY;YACd;YAEA,6BAA6B;YAC7B,kBAAkB;gBAAE,QAAQ;gBAAI,aAAa;YAAG;YAChD,aAAa;YAEb,eAAe;YACf;YACA;QAEF,EAAE,OAAO,KAAU;YACjB,YAAY;gBACV,OAAO;gBACP,SAAS,IAAI,OAAO,IAAI;gBACxB,SAAS;gBACT,YAAY;YACd;QACF,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,0NAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACjC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,4NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QAC7B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAIA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,CAAC,sBAAsB,CAAC,QAAQ;YAClC,OAAO;gBAAE,UAAU;gBAAG,eAAe;gBAAG,WAAW;gBAAG,gBAAgB;gBAAG,WAAW;YAAE;QACxF;QAEA,MAAM,WAAW,mBAAmB,QAAQ;QAC5C,MAAM,gBAAgB,AAAC,SAAS,mBAAmB,aAAa,GAAI;QACpE,MAAM,YAAY,WAAW;QAC7B,MAAM,iBAAiB,SAAS;QAChC,MAAM,YAAY;QAElB,OAAO;YAAE;YAAU;YAAe;YAAW;YAAgB;QAAU;IACzE;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,CAAC,YAAY;QACf,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,sMAAA,CAAA,SAAM;QAAC;QAClD;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,4NAAA,CAAA,gBAAa;QAAC;QACvD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,0NAAA,CAAA,eAAY;QAAC;KACzD;IAED,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,2IAAA,CAAA,cAAW;;;;;YACrB,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB,kBAC5B,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;8BACC,cAAA,8OAAC,oIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CACtC,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAyC;;;;;;sEACtD,8OAAC;4DAAE,WAAU;sEACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO;;;;;;;;;;;;8DAGtC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,aAAa;oDAC5B,SAAQ;oDACR,WAAU;;sEAEV,8OAAC,4NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAG5C,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,aAAa;oDAC5B,WAAU;oDACV,UAAU,WAAW,OAAO,GAAG;;sEAE/B,8OAAC,0NAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;0CAOjD,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAyC;;;;;;sEACtD,8OAAC;4DAAE,WAAU;sEACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,eAAe;;;;;;;;;;;;8DAG9C,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGrB,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASzD,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,eAAe,CAAC;gDAC/B,WAAU;;kEAEV,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU;gDACV,WAAU;;kEAEV,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAW,CAAC,QAAQ,EAAE,qBAAqB,iBAAiB,IAAI;;;;;;kEAC3E,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAKd,8OAAC,gIAAA,CAAA,cAAW;;8CAEV,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;;;;;;;;;;;wCAKb,6BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,WAAU;sEAET,iBAAiB,GAAG,CAAC,CAAA,qBACpB,8OAAC;oEAAkB,OAAO;8EACvB,SAAS,QAAQ,cAAc,KAAK,OAAO,CAAC,KAAK;mEADvC;;;;;;;;;;;;;;;;8DAMnB,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAC/C,WAAU;sEAET,cAAc,GAAG,CAAC,CAAA,uBACjB,8OAAC;oEAAoB,OAAO;8EACzB,WAAW,QAAQ,eAAe,OAAO,OAAO,CAAC,KAAK;mEAD5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAWxB,mCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;2CAE7B,gBAAgB,MAAM,GAAG,kBAC3B,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,4BACpB,8OAAC;4CAEC,WAAU;4CACV,SAAS,IAAM,uBAAuB;;8DAEtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,mBAAmB,YAAY,IAAI;;;;;;sEAEtC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAsC,YAAY,WAAW;;;;;;8EAC1E,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAsB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,SAAS;;;;;;sFAC1E,8OAAC;4EAAK,WAAU;sFACb,YAAY,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;wEAEhC,YAAY,SAAS,kBACpB,8OAAC;4EACC,WAAU;4EACV,SAAS,CAAC;gFACR,EAAE,eAAe;gFACjB,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE,YAAY,SAAS;4EACvC;4EACA,OAAM;;gFACP;gFACO,YAAY,SAAS,CAAC,SAAS,CAAC,GAAG;gFAAG;;;;;;;;;;;;;;;;;;;;;;;;;8DAMtD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAW,CAAC,mCAAmC,EAAE,oBAAoB,YAAY,IAAI,GAAG;;gEAC3F,YAAY,IAAI,KAAK,gBAAgB,YAAY,IAAI,KAAK,aAAa,MAAM;gEAC7E,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM;;;;;;;sEAEpC,8OAAC;4DAAI,WAAU;sEACZ,cAAc,YAAY,MAAM;;;;;;;;;;;;;2CApChC,YAAY,EAAE;;;;;;;;;yDA2CzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;wCAC5B,CAAC,cAAc,eAAe,SAAS,iBAAiB,KAAK,mBAC5D,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWxD,MAAM,wBAAwB;QAC5B,MAAM,SAAS,WAAW,eAAe,MAAM,KAAK;QACpD,MAAM,iBAAiB,wBAAwB;QAE/C,qBACE,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;8BAIpC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;;wDAAoC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,WAAW;wDAAG;;;;;;;;;;;;;sDAE5F,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAKxB,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;;wDAAoC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB,uBAAuB;wDAAI;;;;;;;;;;;;;sDAEjH,8OAAC,0NAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAK9B,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;sDAElD,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO1B,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAgB;;;;;;;;;;;sCAEvC,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAK,UAAU;gCAAwB,WAAU;;oCAC/C,iCACC,8OAAC;wCAAI,WAAU;kDACZ;;;;;;kDAIL,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,MAAK;gEACL,KAAK,oBAAoB,uBAAuB;gEAChD,KAAK,YAAY,WAAW;gEAC5B,OAAO,eAAe,MAAM;gEAC5B,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEAC/E,aAAY;gEACZ,WAAU;gEACV,QAAQ;;;;;;0EAEV,8OAAC;gEAAE,WAAU;;oEAA6B;oEAClC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB,uBAAuB;oEAAI;;;;;;;;;;;;;kEAIxE,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO,eAAe,WAAW;gEACjC,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEACpF,aAAY;gEACZ,WAAU;gEACV,QAAQ;;;;;;0EAEV,8OAAC;gEAAE,WAAU;0EACV,yBAAyB,eAAe,WAAW,KAAK,wBACrD,mDACA;;;;;;;;;;;;;;;;;;4CAOT,SAAS,mBACR,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,8OAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAwB;;;;;;;;;;;kEAE/C,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC;4EAAK,WAAU;;gFAA+B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;gFAAQ;;;;;;;;;;;;;8EAExE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC;4EAAK,WAAU;;gFAA6B;gFAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,QAAQ;gFAAE;;;;;;;;;;;;;8EAEzF,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;;gFAAgB;gFAAiB,oBAAoB,iBAAiB;gFAAE;;;;;;;sFACxF,8OAAC;4EAAK,WAAU;;gFAA6B;gFAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,aAAa;gFAAE;;;;;;;;;;;;;8EAE9F,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;8FAA4B;;;;;;8FAC5C,8OAAC;oFAAK,WAAU;;wFAA0B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,SAAS;wFAAE;;;;;;;;;;;;;sFAErF,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;8FAA4B;;;;;;8FAC5C,8OAAC;oFAAK,WAAU;;wFAA0B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,cAAc;wFAAE;;;;;;;;;;;;;sFAE1F,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;8FAAmC;;;;;;8FACnD,8OAAC;oFAAK,WAAU;;wFAAoC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,SAAS;wFAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAU3G,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;;wEAAG;wEAAoB,oBAAoB,kBAAkB;wEAAE;;;;;;;8EAChE,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,aAAa;gDAC5B,WAAU;0DACX;;;;;;0DAGD,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS;gDACT,WAAU;gDACV,UACE,CAAC,cACD,CAAC,sBACD,SAAS,CAAC,oBAAoB,uBAAuB,EAAE,KACvD,eAAe,cAAc,GAAG,WAAW,OAAO;0DAGnD,eAAe,cAAc,GAAG,CAAC,YAAY,WAAW,CAAC,IACtD,yBACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASpB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC;wBACT,MAAM,OAAO,IAAI,IAAI;wBACrB,MAAM,WAAW,cAAc,IAAI,EAAE;wBACrC,qBACE,8OAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAC;;kBAEV,EAAE,WACE,oCACA,6EACH;gBACH,CAAC;;8CAED,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;8CAAM,IAAI,KAAK;;;;;;;2BAXX,IAAI,EAAE;;;;;oBAcjB;;;;;;;;;;;YAKH;YAGA,wBAAwB,qCACvB,8OAAC,iIAAA,CAAA,QAAK;gBACJ,QAAQ;gBACR,SAAS,IAAM,wBAAwB;gBACvC,OAAM;gBACN,MAAK;0BAEL,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,mBAAmB,oBAAoB,IAAI;sDAC5C,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DACX,oBAAoB,WAAW;;;;;;8DAElC,8OAAC;oDAAE,WAAU;;wDACV,oBAAoB,IAAI,CAAC,OAAO,CAAC,KAAK;wDAAK;wDAAI,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB,SAAS;;;;;;;;;;;;;;;;;;;8CAIlG,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDACZ,cAAc,oBAAoB,MAAM;8DACzC,8OAAC;oDAAK,WAAW,CAAC,2CAA2C,EAC3D,oBAAoB,MAAM,KAAK,eAAe,oBAAoB,MAAM,KAAK,cAAc,4BAC3F,oBAAoB,MAAM,KAAK,YAAY,gCAC3C,2BACA;8DACC,oBAAoB,MAAM;;;;;;;;;;;;sDAG/B,8OAAC;4CAAE,WAAW,CAAC,kBAAkB,EAAE,oBAAoB,oBAAoB,IAAI,GAAG;;gDAC/E,oBAAoB,IAAI,KAAK,gBAAgB,oBAAoB,IAAI,KAAK,aAAa,MAAM;gDAC7F,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB,MAAM;;;;;;;;;;;;;;;;;;;sCAMhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEACV,oBAAoB,EAAE;;;;;;sEAEzB,8OAAC;4DACC,SAAS,IAAM,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE,oBAAoB,EAAE;4DACrD,WAAU;sEAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAKtB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDAAE,WAAU;8DAAyB,oBAAoB,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;sDAG9E,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDAAI,WAAU;;wDACZ,cAAc,oBAAoB,MAAM;sEACzC,8OAAC;4DAAK,WAAU;sEAAyB,oBAAoB,MAAM;;;;;;;;;;;;;;;;;;sDAIvE,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDAAE,WAAU;8DAAyB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB,SAAS;;;;;;;;;;;;;;;;;;8CAItF,8OAAC;oCAAI,WAAU;;wCACZ,oBAAoB,SAAS,kBAC5B,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEACV,oBAAoB,SAAS;;;;;;sEAEhC,8OAAC;4DACC,SAAS,IAAM,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE,oBAAoB,SAAS;4DAC5D,WAAU;sEAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;wCAMvB,oBAAoB,IAAI,kBACvB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEACV,oBAAoB,IAAI;;;;;;sEAE3B,8OAAC;4DACC,SAAS,IAAM,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE,oBAAoB,IAAI;4DACvD,WAAU;sEAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;wCAMvB,oBAAoB,WAAW,kBAC9B,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DACd,oBAAoB,IAAI,KAAK,YAAY,oBAAoB;;;;;;8DAEhE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEACV,oBAAoB,WAAW;;;;;;sEAElC,8OAAC;4DACC,SAAS,IAAM,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE,oBAAoB,WAAW;4DAC9D,WAAU;sEAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;wCAMvB,oBAAoB,aAAa,KAAK,2BACrC,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDAAE,WAAU;8DAAyB,oBAAoB,aAAa;;;;;;;;;;;;wCAI1E,oBAAoB,WAAW,kBAC9B,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDAAE,WAAU;8DAAyB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;wBAO3F,oBAAoB,eAAe,kBAClC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAgC;;;;;;8CAC9C,8OAAC;oCAAE,WAAU;8CAAwB,oBAAoB,eAAe;;;;;;;;;;;;wBAI3E,oBAAoB,aAAa,kBAChC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAmC,oBAAoB,aAAa;;;;;;sDACjF,8OAAC;4CACC,SAAS,IAAM,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE,oBAAoB,aAAa;4CAChE,WAAU;sDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAOxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,wBAAwB;8CACxC;;;;;;gCAGA,oBAAoB,IAAI,kBACvB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;wCACP,MAAM,cAAc,CAAC,mCAAmC,EAAE,oBAAoB,IAAI,EAAE;wCACpF,OAAO,IAAI,CAAC,aAAa;oCAC3B;oCACA,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC,0NAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpC,8OAAC;;;;;0BACD,8OAAC;;;;;;;;;;;AAGP", "debugId": null}}]}