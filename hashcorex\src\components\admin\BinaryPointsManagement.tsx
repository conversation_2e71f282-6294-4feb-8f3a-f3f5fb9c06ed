'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>eader, CardTitle, CardContent, Input, Button, useConfirmDialog, Modal } from '@/components/ui';
import {
  ArrowUpDown,
  Search,
  Filter,
  Download,
  Eye,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  AlertTriangle,
  Calendar,
  Activity,
  BarChart3,
  X
} from 'lucide-react';
import { formatCurrency, formatNumber, formatDateTime } from '@/lib/utils';

interface BinaryPointsData {
  id: string;
  userId: string;
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    createdAt: string;
  };
  leftPoints: number;
  rightPoints: number;
  matchedPoints: number;
  totalMatched: number;
  lastMatchDate: string | null;
  flushDate: string | null;
  createdAt: string;
  updatedAt: string;
}

interface BinaryMatchHistory {
  id: string;
  userId: string;
  user: {
    email: string;
    firstName: string;
    lastName: string;
  };
  matchedPoints: number;
  payout: number;
  leftPointsBefore: number;
  rightPointsBefore: number;
  leftPointsAfter: number;
  rightPointsAfter: number;
  matchDate: string;
  type: 'WEEKLY' | 'MANUAL';
}

export const BinaryPointsManagement: React.FC = () => {
  const [binaryPointsData, setBinaryPointsData] = useState<BinaryPointsData[]>([]);
  const [matchHistory, setMatchHistory] = useState<BinaryMatchHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all');
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [selectedUserData, setSelectedUserData] = useState<BinaryPointsData | null>(null);
  const [userMatchHistory, setUserMatchHistory] = useState<BinaryMatchHistory[]>([]);
  const [showUserModal, setShowUserModal] = useState(false);
  const [loadingUserData, setLoadingUserData] = useState(false);
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalLeftPoints: 0,
    totalRightPoints: 0,
    totalMatchedPoints: 0,
    totalPayouts: 0,
  });
  const [maxPointsPerSide, setMaxPointsPerSide] = useState(10);
  const [newLimit, setNewLimit] = useState('10');
  const [updatingLimit, setUpdatingLimit] = useState(false);

  const { showConfirm, ConfirmDialog } = useConfirmDialog();

  useEffect(() => {
    fetchBinaryPointsData();
    fetchMatchHistory();
    fetchCurrentLimit();
  }, []);

  const fetchCurrentLimit = async () => {
    try {
      const response = await fetch('/api/admin/settings', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const limit = data.data.maxBinaryPointsPerSide || 10;
          setMaxPointsPerSide(limit);
          setNewLimit(limit.toString());
          console.log(`Current binary points limit: ${limit}`);
        }
      }
    } catch (error) {
      console.error('Failed to fetch current limit:', error);
    }
  };

  const fetchBinaryPointsData = async () => {
    try {
      const response = await fetch('/api/admin/binary-points', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setBinaryPointsData(data.data);
          calculateStats(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch binary points data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchMatchHistory = async () => {
    try {
      const response = await fetch('/api/admin/binary-points/history', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setMatchHistory(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch match history:', error);
    }
  };

  const calculateStats = (data: BinaryPointsData[]) => {
    const stats = data.reduce((acc, item) => ({
      totalUsers: acc.totalUsers + 1,
      totalLeftPoints: acc.totalLeftPoints + item.leftPoints,
      totalRightPoints: acc.totalRightPoints + item.rightPoints,
      totalMatchedPoints: acc.totalMatchedPoints + item.matchedPoints,
      totalPayouts: acc.totalPayouts + (item.totalMatched * 10), // Assuming $10 per point
    }), {
      totalUsers: 0,
      totalLeftPoints: 0,
      totalRightPoints: 0,
      totalMatchedPoints: 0,
      totalPayouts: 0,
    });

    setStats(stats);
  };

  const filteredData = binaryPointsData.filter(item => {
    const matchesSearch = 
      item.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.user.lastName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = 
      filterStatus === 'all' ||
      (filterStatus === 'active' && (item.leftPoints > 0 || item.rightPoints > 0)) ||
      (filterStatus === 'inactive' && item.leftPoints === 0 && item.rightPoints === 0);

    return matchesSearch && matchesFilter;
  });

  const exportData = () => {
    const csvContent = [
      ['User Email', 'Name', 'Left Points', 'Right Points', 'Matched Points', 'Total Matched', 'Last Match Date'].join(','),
      ...filteredData.map(item => [
        item.user.email,
        `${item.user.firstName} ${item.user.lastName}`,
        item.leftPoints,
        item.rightPoints,
        item.matchedPoints,
        item.totalMatched,
        item.lastMatchDate ? formatDateTime(item.lastMatchDate) : 'Never'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `binary-points-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const viewUserHistory = async (userId: string) => {
    setSelectedUser(userId);
    setLoadingUserData(true);
    setShowUserModal(true);

    // Find user data
    const userData = binaryPointsData.find(user => user.userId === userId);
    setSelectedUserData(userData || null);

    try {
      // Fetch user-specific match history
      const response = await fetch(`/api/admin/binary-points/user-history/${userId}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setUserMatchHistory(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch user match history:', error);
    } finally {
      setLoadingUserData(false);
    }
  };



  const handleUpdateLimit = async () => {
    const limit = parseFloat(newLimit);
    if (isNaN(limit) || limit <= 0) {
      alert('Please enter a valid positive number for the limit');
      return;
    }

    const confirmed = await showConfirm({
      title: 'Update Binary Points Limit',
      message: `Are you sure you want to update the maximum points per side to ${limit}? This will affect all future binary point additions.`,
      confirmText: 'Update Limit',
      cancelText: 'Cancel',
    });

    if (!confirmed) return;

    setUpdatingLimit(true);
    try {
      const response = await fetch('/api/admin/binary-points/update-limit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ maxPointsPerSide: limit }),
      });

      const data = await response.json();

      if (data.success) {
        setMaxPointsPerSide(limit);
        alert(`Successfully updated binary points limit to ${limit}`);
      } else {
        alert(`Failed to update limit: ${data.error}`);
      }
    } catch (error) {
      console.error('Update limit error:', error);
      alert('Failed to update binary points limit');
    } finally {
      setUpdatingLimit(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Current Limit Display */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <AlertTriangle className="h-5 w-5 text-orange-400" />
            Binary Points Limit Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 items-end">
            <div className="flex-1">
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Maximum Points Per Side
              </label>
              <Input
                type="number"
                value={newLimit}
                onChange={(e) => setNewLimit(e.target.value)}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="Enter limit"
                min="1"
                step="0.1"
              />
            </div>
            <Button
              onClick={handleUpdateLimit}
              disabled={updatingLimit || newLimit === maxPointsPerSide.toString()}
              className="bg-orange-600 text-white"
            >
              {updatingLimit ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Updating...
                </>
              ) : (
                'Update Limit'
              )}
            </Button>
          </div>
          <p className="text-sm text-slate-400 mt-2">
            Current limit: <span className="text-orange-400 font-medium">{maxPointsPerSide} points per side</span>
          </p>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Total Users</p>
                <p className="text-2xl font-bold text-white">{formatNumber(stats.totalUsers)}</p>
              </div>
              <Users className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Total Left Points</p>
                <p className="text-2xl font-bold text-white">{formatNumber(stats.totalLeftPoints)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Total Right Points</p>
                <p className="text-2xl font-bold text-white">{formatNumber(stats.totalRightPoints)}</p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Total Matched</p>
                <p className="text-2xl font-bold text-white">{formatNumber(stats.totalMatchedPoints)}</p>
              </div>
              <ArrowUpDown className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Total Payouts</p>
                <p className="text-2xl font-bold text-white">{formatCurrency(stats.totalPayouts)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <ArrowUpDown className="h-5 w-5 text-purple-400" />
            Binary Points Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Controls */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search by email or name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm"
              >
                <option value="all">All Users</option>
                <option value="active">Active Points</option>
                <option value="inactive">No Points</option>
              </select>

              <Button
                onClick={exportData}
                className="bg-green-600 text-white"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>

          {/* Table */}
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-slate-600">
                  <th className="text-left py-3 px-4 text-slate-300">User</th>
                  <th className="text-right py-3 px-4 text-slate-300">Left Points</th>
                  <th className="text-right py-3 px-4 text-slate-300">Right Points</th>
                  <th className="text-right py-3 px-4 text-slate-300">Matchable</th>
                  <th className="text-right py-3 px-4 text-slate-300">Total Matched</th>
                  <th className="text-right py-3 px-4 text-slate-300">Last Match</th>
                  <th className="text-center py-3 px-4 text-slate-300">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredData.map((item) => {
                  const matchablePoints = Math.min(item.leftPoints, item.rightPoints);
                  return (
                    <tr key={item.id} className="border-b border-slate-700">
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium text-white">
                            {item.user.firstName} {item.user.lastName}
                          </div>
                          <div className="text-sm text-slate-400">{item.user.email}</div>
                        </div>
                      </td>
                      <td className="text-right py-3 px-4 text-white">
                        {formatNumber(item.leftPoints)}
                      </td>
                      <td className="text-right py-3 px-4 text-white">
                        {formatNumber(item.rightPoints)}
                      </td>
                      <td className="text-right py-3 px-4">
                        <span className={`font-medium ${matchablePoints > 0 ? 'text-green-400' : 'text-slate-400'}`}>
                          {formatNumber(matchablePoints)}
                        </span>
                      </td>
                      <td className="text-right py-3 px-4 text-white">
                        {formatNumber(item.totalMatched)}
                      </td>
                      <td className="text-right py-3 px-4 text-slate-300">
                        {item.lastMatchDate ? formatDateTime(item.lastMatchDate) : 'Never'}
                      </td>
                      <td className="text-center py-3 px-4">
                        <Button
                          onClick={() => viewUserHistory(item.userId)}
                          className="bg-blue-600 text-white text-xs px-2 py-1"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </Button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {filteredData.length === 0 && (
            <div className="text-center py-8 text-slate-400">
              No binary points data found matching your criteria.
            </div>
          )}
        </CardContent>
      </Card>

      {/* User Details Modal */}
      {showUserModal && selectedUserData && (
        <Modal
          isOpen={showUserModal}
          onClose={() => {
            setShowUserModal(false);
            setSelectedUser(null);
            setSelectedUserData(null);
            setUserMatchHistory([]);
          }}
          title={`Binary Points Details - ${selectedUserData.user.firstName} ${selectedUserData.user.lastName}`}
          size="xl"
          darkMode={true}
        >
          <div className="space-y-6">
            {/* User Info Header */}
            <div className="bg-slate-700 rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">User Information</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-300">Name:</span>
                      <span className="text-white">{selectedUserData.user.firstName} {selectedUserData.user.lastName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-300">Email:</span>
                      <span className="text-white">{selectedUserData.user.email}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-300">Joined:</span>
                      <span className="text-white">{formatDateTime(selectedUserData.user.createdAt)}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Current Status</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-300">Left Points:</span>
                      <span className="text-green-400 font-semibold">{formatNumber(selectedUserData.leftPoints)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-300">Right Points:</span>
                      <span className="text-orange-400 font-semibold">{formatNumber(selectedUserData.rightPoints)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-300">Total Matched:</span>
                      <span className="text-purple-400 font-semibold">{formatNumber(selectedUserData.totalMatched)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-300">Last Match:</span>
                      <span className="text-white">
                        {selectedUserData.lastMatchDate ? formatDateTime(selectedUserData.lastMatchDate) : 'Never'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Current Points Analysis */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-slate-400">Matchable Points</p>
                      <p className="text-2xl font-bold text-white">
                        {formatNumber(Math.min(selectedUserData.leftPoints, selectedUserData.rightPoints))}
                      </p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-purple-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-slate-400">Potential Payout</p>
                      <p className="text-2xl font-bold text-white">
                        {formatCurrency(Math.min(selectedUserData.leftPoints, selectedUserData.rightPoints) * 10)}
                      </p>
                    </div>
                    <DollarSign className="h-8 w-8 text-green-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-slate-400">Total Earned</p>
                      <p className="text-2xl font-bold text-white">
                        {formatCurrency(selectedUserData.totalMatched * 10)}
                      </p>
                    </div>
                    <Activity className="h-8 w-8 text-blue-400" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Match History */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Calendar className="h-5 w-5 text-blue-400" />
                Binary Matching History
              </h3>

              {loadingUserData ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto"></div>
                  <p className="text-slate-400 mt-2">Loading match history...</p>
                </div>
              ) : userMatchHistory.length > 0 ? (
                <div className="bg-slate-800 rounded-lg overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead className="bg-slate-700">
                        <tr>
                          <th className="text-left py-3 px-4 text-slate-300">Date</th>
                          <th className="text-right py-3 px-4 text-slate-300">Matched Points</th>
                          <th className="text-right py-3 px-4 text-slate-300">Payout</th>
                          <th className="text-right py-3 px-4 text-slate-300">Left Before</th>
                          <th className="text-right py-3 px-4 text-slate-300">Right Before</th>
                          <th className="text-right py-3 px-4 text-slate-300">Left After</th>
                          <th className="text-right py-3 px-4 text-slate-300">Right After</th>
                          <th className="text-center py-3 px-4 text-slate-300">Type</th>
                        </tr>
                      </thead>
                      <tbody>
                        {userMatchHistory.map((match, index) => (
                          <tr key={match.id} className={index % 2 === 0 ? 'bg-slate-800' : 'bg-slate-750'}>
                            <td className="py-3 px-4 text-white">
                              {formatDateTime(match.matchDate)}
                            </td>
                            <td className="text-right py-3 px-4 text-purple-400 font-semibold">
                              {formatNumber(match.matchedPoints)}
                            </td>
                            <td className="text-right py-3 px-4 text-green-400 font-semibold">
                              {formatCurrency(match.payout)}
                            </td>
                            <td className="text-right py-3 px-4 text-slate-300">
                              {formatNumber(match.leftPointsBefore)}
                            </td>
                            <td className="text-right py-3 px-4 text-slate-300">
                              {formatNumber(match.rightPointsBefore)}
                            </td>
                            <td className="text-right py-3 px-4 text-green-400">
                              {formatNumber(match.leftPointsAfter)}
                            </td>
                            <td className="text-right py-3 px-4 text-orange-400">
                              {formatNumber(match.rightPointsAfter)}
                            </td>
                            <td className="text-center py-3 px-4">
                              <span className={`px-2 py-1 rounded text-xs ${
                                match.type === 'WEEKLY'
                                  ? 'bg-blue-600 text-white'
                                  : 'bg-orange-600 text-white'
                              }`}>
                                {match.type}
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 bg-slate-800 rounded-lg">
                  <Calendar className="h-12 w-12 text-slate-600 mx-auto mb-2" />
                  <p className="text-slate-400">No binary matching history found for this user.</p>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-slate-700">
              <Button
                variant="outline"
                onClick={() => {
                  setShowUserModal(false);
                  setSelectedUser(null);
                  setSelectedUserData(null);
                  setUserMatchHistory([]);
                }}
                className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600"
              >
                Close
              </Button>
            </div>
          </div>
        </Modal>
      )}

      {/* Confirmation Dialog */}
      <ConfirmDialog />
    </div>
  );
};
